
--############################
--############################
--#######FRAMEWORK#########
--############################
--###########################

if Config.submarineheist.Framework == 'ESX' then
    ESX = Config.Framework_core
elseif Config.submarineheist.Framework == 'QB' then
    QBCore = Config.Framework_core
elseif Config.submarineheist.Framework == 'CUSTOM' then
    Custom = Config.Framework_core
else
    print("Error: Invalid framework specified in Config.submarineheist.Framework.")
    return
end

Citizen.CreateThread(function()
if Config.submarineheist.Framework == 'ESX' then
ESX.RegisterUsableItem(Config.submarineheistitems.Detonate.item, function(source)
local xPlayer = ESX.GetPlayerFromId(source)
    TriggerClientEvent('sb_subheist:client:plantedbomb', source)
end)
ESX.RegisterUsableItem(Config.submarineheistitems.Ocygenmask.item, function(source)
local xPlayer = ESX.GetPlayerFromId(source)
    TriggerClientEvent('sb_subheist:diveroxy', source)
	xPlayer.removeInventoryItem(Config.submarineheistitems.Ocygenmask.item, 1)
end)
elseif Config.submarineheist.Framework == 'QB' then
QBCore.Functions.CreateUseableItem(Config.submarineheistitems.Detonate.item, function(source)
    local Player = QBCore.Functions.GetPlayer(source)
    TriggerClientEvent('sb_subheist:client:plantedbomb', source)
end)
QBCore.Functions.CreateUseableItem(Config.submarineheistitems.Ocygenmask.item, function(source)
    local Player = QBCore.Functions.GetPlayer(source)
    TriggerClientEvent('sb_subheist:diveroxy', source)
	Player.Functions.RemoveItem(Config.submarineheistitems.Ocygenmask.item, 1)
end)
else
    print("Error: Invalid framework specified in Config.submarineheist.Framework.")
    return
end
end)

--############################
--############################
--#######CALLBACKS#########
--############################
--###########################


InitializeCallbacks = function()
    local framework = Config.submarineheist.Framework
    if framework == 'ESX' then      

        ESX.RegisterServerCallback('sb_subheist:checkitem', function(source, cb, item, amount)
            local xPlayer = ESX.GetPlayerFromId(source)

            if xPlayer.getInventoryItem(item).count >= amount then
                xPlayer.removeInventoryItem(item, amount)
                cb(true)
            else
                cb(false)
            end
        end)
    elseif framework == 'QB' then

        QBCore.Functions.CreateCallback('sb_subheist:checkitem', function(source, cb, item, amount)
            local src = source
            local player = QBCore.Functions.GetPlayer(src)
            local playerItem = player.Functions.GetItemByName(item)
        
            if player then 
                if playerItem ~= nil then
                    if playerItem.amount >= amount then
                        player.Functions.RemoveItem(item, amount)
                        cb(true)
                    end
                else
                    cb(false)
                end
            end
        end)
    elseif framework == 'CUSTOM' then
        -- Handle your custom framework here
    end
end

--############################
--############################
--#######REWARDS#########
--############################
--###########################


RegisterServerEvent('sb_subheist:reward')
AddEventHandler('sb_subheist:reward', function(item, amount)
    local src = source
    if Config.submarineheist.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        xPlayer.addInventoryItem(item, amount)
        TriggerClientEvent('sb_subheist:Notify', source, Languages['received'] .. item)
        DiscordLogreward(xPlayer.getName() .. " Has received " .. item .. " " .. amount)
    elseif Config.submarineheist.Framework == 'QB' then
        local player = QBCore.Functions.GetPlayer(src)
        player.Functions.AddItem(item, amount)
        TriggerClientEvent('sb_subheist:Notify', source, Languages['received'] .. item)
        DiscordLogreward(player.PlayerData.name .. " Has received " .. item .. " " .. amount)
    elseif Config.submarineheist.Framework == 'CUSTOM' then
          -- Handle your custom framework here	
    end
end)

RegisterServerEvent('sb_subheist:dentoremove:server')
AddEventHandler('sb_subheist:dentoremove:server', function()
    local src = source
    if Config.submarineheist.Framework == 'ESX' then
    local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeInventoryItem(Config.submarineheistitems.Detonate.item, 1)
    elseif Config.submarineheist.Framework == 'QB' then
    local Player = QBCore.Functions.GetPlayer(source)
	Player.Functions.RemoveItem(Config.submarineheistitems.Detonate.item, 1)
    elseif Config.submarineheist.Framework == 'CUSTOM' then
          -- Handle your custom framework here	
    end
end)

RegisterServerEvent('sb_subheist:rewardmoney')
AddEventHandler('sb_subheist:rewardmoney', function()
    if Config.submarineheist.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        for k, v in pairs(Config.rewards) do
            local auctionitem = xPlayer.getInventoryItem(v.item)
            if auctionitem.count >= 1 then
                xPlayer.removeInventoryItem(v.item, auctionitem.count)
                if Config.submarineheist.Reward_black then
                    xPlayer.addAccountMoney(Config.submarineheist.blacmoney_name, v.price * auctionitem.count)
                else
                    xPlayer.addMoney(v.price * auctionitem.count)
                end
                TriggerClientEvent('sb_subheist:Notify', source, Languages['received'] .. v.price * auctionitem.count)
                DiscordLogreward(xPlayer.getName() .. " Has received " .. (Config.submarineheist.Reward_black and Config.submarineheist.blacmoney_name or "Cash") .. " " .. v.price * auctionitem.count)
            else
                TriggerClientEvent('sb_subheist:Notify', source, Languages['not_enough'])
            end
        end
    elseif Config.submarineheist.Framework == 'QB' then
        local player = QBCore.Functions.GetPlayer(source)
        for k, v in pairs(Config.rewards) do
            local auctionitem = player.Functions.GetItemByName(v.item)
            if auctionitem ~= nil and auctionitem.amount > 0 then
                player.Functions.RemoveItem(v.item, auctionitem.amount)
                if Config.submarineheist.Reward_black then
                    player.Functions.AddItem(Config.submarineheist.blacmoney_name, v.price * auctionitem.amount)
                else
                    player.Functions.AddMoney('cash', v.price * auctionitem.amount)
                end
                TriggerClientEvent('sb_subheist:Notify', source, Languages['received'] .. v.price * auctionitem.amount, 'success', 10000)
                local charinfo = player.PlayerData.charinfo
			    local playername = charinfo and (charinfo.firstname .. " " .. charinfo.lastname) or player.PlayerData.name
				DiscordLogreward(playername .. " Has received " .. (Config.submarineheist.Reward_black and Config.submarineheist.blacmoney_name or "Cash") .. " " .. v.price * auctionitem.amount)
            else
                TriggerClientEvent('sb_subheist:Notify', source, Languages['not_enough'])
            end
        end
		elseif Config.submarineheist.Framework == 'CUSTOM' then
          -- Handle your custom framework here	
    end
end)


--############################
--############################
--#######POLICE&COOLDOWN#########
--############################
--###########################


RegisterServerEvent('sb_subheist:startrobbery')
AddEventHandler('sb_subheist:startrobbery', function()
    local src = source
    local isActive, remaintime = iscooldownactiveship("global") 

    if isActive then
        TriggerClientEvent('sb_subheist:Notify', src, Languages['recent_robbed'] .. " (" .. remaintime .. " seconds left)", 'error', 10000)
        return
    end

    if Config.submarineheist.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(src)
        local xPlayers = ESX.GetExtendedPlayers('job', Config.police.Policejobname)
        local police = 0

        for _, officer in pairs(xPlayers) do
            police = police + 1
        end

        if police >= Config.police.Count then
            resetpolice()
            TriggerClientEvent('sb_subheist:client:notistart', src)
            DiscordLogstart(xPlayer.getName() .. " has started Ship Heist")
            startcooldownship("global") 
        else
            TriggerClientEvent('sb_subheist:Notify', src, Languages['no_police'], 'error', 10000)
        end
    elseif Config.submarineheist.Framework == 'QB' then
        local players = QBCore.Functions.GetPlayers()
		local player = QBCore.Functions.GetPlayer(src)
        local police = 0

        for i = 1, #players do
            local player = QBCore.Functions.GetPlayer(players[i])
            if player.PlayerData.job.name == Config.police.Policejobname then
                police = police + 1
            end
        end

        if police >= Config.police.Count then
            resetpolice()
			local charinfo = player.PlayerData.charinfo
			local playername = charinfo and (charinfo.firstname .. " " .. charinfo.lastname) or player.PlayerData.name
            TriggerClientEvent('sb_subheist:client:notistart', src)
            DiscordLogstart(playername .. " has started Ship Heist")
            startcooldownship("global") 
        else
            TriggerClientEvent('sb_subheist:Notify', src, Languages['no_police'], 'error', 10000)
        end
    elseif Config.submarineheist.Framework == 'CUSTOM' then
          -- Handle your custom framework here	
    end
end)




--############################
--############################
--#######POLICE ALERT#########
--############################
--###########################


RegisterServerEvent('sb_subheist:server:policeAlert')
AddEventHandler('sb_subheist:server:policeAlert', function()
    if Config.submarineheist.Framework == 'ESX' then
         local xPlayers = ESX.GetExtendedPlayers('job', Config.police.Policejobname)
         for _, xPlayer in pairs(xPlayers) do
         TriggerClientEvent('sb_subheist:policeAlert', xPlayer.source)
	end
    elseif Config.submarineheist.Framework == 'QB' then
        local players = QBCore.Functions.GetPlayers()
        for i = 1, #players do
            local player = QBCore.Functions.GetPlayer(players[i])
            if player.PlayerData.job.name == Config.police.Policejobname then
                TriggerClientEvent('sb_subheist:policeAlert', player.PlayerData.source)
            end
        end
		elseif Config.submarineheist.Framework == 'CUSTOM' then
          -- Handle your custom framework here	
    end
end)


--############################
--############################
--#######HEIST RESET#########
--############################
--###########################


Citizen.CreateThread(function()
    if Config.submarineheist.Framework == 'ESX' then
        ESX.RegisterServerCallback('sb_subheist:checkAdmin', function(source, cb)
            if Config.submarineheist.Resetheist_admin then
                local identifier = PlayerIdentifier('license', source)
                for i = 1, #Config.adminlist, 1 do
                    if identifier == Config.adminlist[i] then
                        cb(1)
				        resetcooldownship("global")
	                    resetfunction()
                    else
                        cb(3)
                    end
                end
            end
        end)
    elseif Config.submarineheist.Framework == 'QB' then
        QBCore.Functions.CreateCallback('sb_subheist:checkAdmin', function(source, cb)
            if Config.submarineheist.Resetheist_admin then
                local identifier = PlayerIdentifier('license', source)
                for i = 1, #Config.adminlist, 1 do
                    if identifier == Config.adminlist[i] then                     
                        cb(1)
					    resetcooldownship("global")
						resetfunction()
                    else
                        cb(3)
                    end
                end
            end
        end)
    elseif Config.submarineheist.Framework == 'CUSTOM' then
        -- Handle your custom framework here	
    end
end)

function GetPlayerNameSafe(src)
    return GetPlayerName(src) or "Unknown"
end

--############################
--############################
--#######DISCORD LOGS#########
--############################
--###########################


function DiscordLogreward(desc)
    if not Config.Discordlogs then return end

    local content = {
        {
        	["color"] = '5015295',
            ["title"] = "SB Submarine Heist Log",
            ["description"] = desc,
            ["footer"] = {
                ["text"] = GetCurrentResourceName()
            },
        }
    }
  	PerformHttpRequest(Config.WebhookURL_Reward, function(err, text, headers) end, 'POST', json.encode({embeds = content}), { ['Content-Type'] = 'application/json' })
end

function DiscordLogstart(desc)
    if not Config.Discordlogs then return end

    local content = {
        {
        	["color"] = '5015295',
            ["title"] = "SB Submarine Heist Log",
            ["description"] = desc,
            ["footer"] = {
                ["text"] = GetCurrentResourceName()
            },
        }
    }
  	PerformHttpRequest(Config.WebhookURL_Start, function(err, text, headers) end, 'POST', json.encode({embeds = content}), { ['Content-Type'] = 'application/json' })
end


function Discordloglogout(title, description, color)
    PerformHttpRequest(Config.Logoutwebhook, function(err, text, headers) end, 'POST', json.encode({
        embeds = {
            {
                title = title,
                description = description,
                color = color or 16777215 
            }
        }
    }), { ['Content-Type'] = 'application/json' })
end