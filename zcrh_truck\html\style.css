@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lobster&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Righteous&display=swap');

body{
    font-family: '<PERSON>', sans-serif;
    font-weight: bold;
    display: block;
    user-select: none;
    overflow: hidden;
}

.main-section{
    height: 100vh;
    width: 100vw;
}

.main-section .bg{
    position: absolute;
    top: -50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 50%;
    width: 200%;
    background-color: rgba(0, 0, 0, 0.648);
    border-radius: 50%;
    filter: blur(80px);
    transition: all ease-in-out 0.4s;
}

.main-section h1{
    position: absolute;
    top: -50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 2vw;
    z-index: 2;
    max-width: 80%;
    transition: all ease-in-out 0.4s;
    transition-delay: 0.2s;
}