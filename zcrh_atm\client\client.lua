local ESX = nil
local playerPed, playerCoords, Tablet
local PlayerJob = {}
local MiniGame ={}
local HackStatus = false
local NotifyMessage = ''
local NotifyType = 'primay'
local Hacked = {}

ESX = exports["es_extended"]:getSharedObject()

-- qtarget Exports

exports['qtarget']:AddTargetModel(Config.ATMModels, {
	options = {
		{
			event = "zcrh_atm:client:starthack",
			icon = "fa-solid fa-mobile-screen-button",
			label = 'Hack ATM',
		}
	},
	job = {"all"},
	distance = 1.5
})
--[[
exports['qtarget']:AddTargetModel(Config.ATMModels[2], {
	options = {
		{
			event = "zcrh_atm:client:starthack",
			icon = "fa-solid fa-mobile-screen-button",
			label = 'Hack ATM',
		},
	},
	job = {"all"},
	distance = 1.5
})

exports['qtarget']:AddTargetModel(Config.ATMModels[3], {
	options = {
		{
			event = "zcrh_atm:client:starthack",
			icon = "fa-solid fa-mobile-screen-button",
			label = 'Hack ATM',
		},
	},
	job = {"all"},
	distance = 1.5
})

]]--

exports['qtarget']:AddTargetModel(Config.ATMModels[4], {
	options = {
		{
			event = "zcrh_atm:client:starthack",
			icon = "fa-solid fa-mobile-screen-button",
			label = 'Hack ATM',
		},
	},
	job = {"all"},
	distance = 1.5
})


-- NUI

MiniGame.Open = function()
	SendNUIMessage({
		action = "open",
		base = Config.MingameBase, 
		timeLimit = Config.MinigameTimeLimit,
		attemptLimit = Config.MingameAttemptLimit 
	})
	SetNuiFocus(true, true)
end

RegisterNUICallback('close', function(data, cb)

	if ESX.GetPlayerData().job.name == 'rea' then
		SetNuiFocus(false, false)
		ClearPedTasks(PlayerPedId())
		return 
	end

	cb('ok')

	HackStatus = data
	Citizen.Wait(500)
	ClearPedTasks(PlayerPedId())
	Citizen.Wait(500)
	DeleteObject(Tablet)
	if HackStatus then
		NotifyMessage = 'Hack Successful!'
		NotifyType = 'success'
		TriggerServerEvent("zcrh_atm:server:givecash")
		-- Success notification now handled by server with actual cash amount
		-- Police already alerted when hack started
	else
		NotifyMessage = 'Hack Failed!'
		NotifyType = 'error'
		-- Trigger heist-notify failure
		if Config.HeistNotifications.Failed.Use then
			TriggerEvent('heist-notify:show', Config.HeistNotifications.Failed, nil, 5, true)
		end
		-- Notify HMS that hack failed
		TriggerServerEvent("zcrh_atm:server:hackfailed")
		-- Police already alerted when hack started
	end
	ESX.ShowNotification(NotifyMessage)
	SetNuiFocus(false, false)
	ClearPedTasks(PlayerPedId())
	DeleteObject(Tablet)
	TriggerServerEvent("zcrh_atm:server:removeitem", Config.RequiredItem)
end)


-- Events

RegisterNetEvent('zcrh_atm:client:starthack')
AddEventHandler('zcrh_atm:client:starthack', function()
	ATMHack()
end)

RegisterNetEvent('zcrh_atm:client:alertcops')
AddEventHandler('zcrh_atm:client:alertcops', function()
	-- Wait(120000)
	
	local data = exports['cd_dispatch']:GetPlayerInfo()
	TriggerServerEvent('cd_dispatch:AddNotification', {
		job_table = {'police', }, 
		coords = data.coords,
		title = '10-15 - ATM Robbery',
		message = 'A '..data.sex..' is robbing an ATM at '..data.street, 
		flash = 1,
		unique_id = data.unique_id,
		sound = 1,
		blip = {
			sprite = 431, 
			scale = 1.2, 
			colour = 3,
			flashes = false, 
			text = 'Suspicious Activity at ATM ',
			time = 5,
			radius = 5,
		}
	})

	ESX.ShowNotification('An alarm has been triggered. ZCPD on the way!')
end)


-- Functions

function ATMHack()
	playerPed = PlayerPedId()
	playerCoords = GetEntityCoords(playerPed, true)
	local nearATM = false
	local hackStarted = false
	local beenHacked = false

	-- Check police count first
	ESX.TriggerServerCallback("zcrh_atm:server:checkPolice", function(enoughPolice)
		if not enoughPolice then
			ESX.ShowNotification('Not enough police in the city! Need at least ' .. Config.RequiredCopsCount .. ' officers.')
			return
		end

		-- Check if player has required item
		ESX.TriggerServerCallback("zcrh_atm:server:hasitem", function(hasItem)
			if(hasItem) then
				-- Notify HMS that ATM heist started
				TriggerServerEvent('hms:heistStarted', 'atmrobbery')
				-- Alert police immediately when hack starts
				TriggerEvent("zcrh_atm:client:alertcops")
			for k, v in pairs(Config.ATMModelsString) do
				local hash = GetHashKey(v)
				Citizen.Wait(50)
				nearATM = IsObjectNearPoint(hash, playerCoords.x, playerCoords.y, playerCoords.z, 1.5)
				Citizen.Wait(50)
				if nearATM then
					hackStarted = true
					local ATM = GetClosestObjectOfType(playerCoords.x, playerCoords.y, playerCoords.z, 1.5, hash, false, false, false)
					for i = 1, #Hacked do
						if Hacked[i] == ATM then
							beenHacked = true
						end
					end
					if not beenHacked then
						RequestAnimDict('amb@prop_human_atm@male@enter')
						while not HasAnimDictLoaded('amb@prop_human_atm@male@enter') do
							Citizen.Wait(5)
						end
						if HasAnimDictLoaded('amb@prop_human_atm@male@enter') then
							TaskPlayAnim(playerPed, 'amb@prop_human_atm@male@enter', "enter", 1.0,-1.0, 3000, 1, 1, true, true, true)
							Citizen.Wait(1000)
							Tablet = CreateObject(GetHashKey("hei_prop_dlc_tablet"), 0, 0, 0, true, true, true)
							AttachEntityToEntity(Tablet, playerPed, GetPedBoneIndex(PlayerPedId(), 18905), 0.20, 0.12, 0.05, 58.0, 122.0, 180.0, true, true, false, true, 1, true)
							RequestAnimDict('missfam4')
							while not HasAnimDictLoaded('missfam4') do
								Citizen.Wait(5)
							end
							if HasAnimDictLoaded('missfam4') then
								TaskPlayAnim(playerPed, "missfam4", "base", 1.0, 4.0, -1, 33, 0.0, false, false, false)
								Citizen.Wait(2000)
							end
						end
						table.insert(Hacked, ATM)
						MiniGame.Open()

					else
						ESX.ShowNotification('Already hacked!')
					end
				else
					if not hackStarted and k == #Config.ATMModelsString then
						ESX.ShowNotification('You cannot do that here.')
					end
				end
			end
		else
			ESX.ShowNotification('You need a Trojan USB to hack an ATM!')
		end
	end, Config.RequiredItem)
	end)
end
