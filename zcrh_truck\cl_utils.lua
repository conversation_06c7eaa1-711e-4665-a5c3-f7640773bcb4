RegisterNetEvent('zcrh_truck:client:DefaultNotify')
AddEventHandler('zcrh_truck:client:DefaultNotify', function(text)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(text)
    DrawNotification(0,1)
end)

RegisterNetEvent('zcrh_truck:client:PoliceAlert')
AddEventHandler('zcrh_truck:client:PoliceAlert', function(coords)
    notification('ROBBERY PROCESS', 'Truck Robbery in progress! Marked on the map!', 10000, 'info')

    AlertPlace = AddBlipForCoord(coords[1], coords[2], coords[3])
    SetBlipSprite(AlertPlace, Config.PoliceAlertBlip.sprite)
    SetBlipScale(AlertPlace, Config.PoliceAlertBlip.size)
    SetBlipColour(AlertPlace, Config.PoliceAlertBlip.color)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentSubstringPlayerName(Config.PoliceAlertBlip.label)
    EndTextCommandSetBlipName(AlertPlace)
end)

function PlayerDied()
    local died = false
    if (GetEntityHealth(PlayerPedId()) <= PlayerDiedHealth) then
        died = true
    end
    return died
end

function NoCarryWeapon()
    if GetSelectedPedWeapon(PlayerPedId()) == GetHashKey('WEAPON_UNARMED') then
        return true
    else
        SendNotify(9)
        return false
    end
end