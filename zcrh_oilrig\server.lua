local heist = {}


RegisterServerEvent("JustHeist:startheist")
AddEventHandler("JustHeist:startheist", function()
    heist[source] = 1
    -- Notify HMS that oil rig heist started
    TriggerEvent('hms:heistStarted', 'oilrig')
end)

RegisterServerEvent("JustHeist:checkifstarted")
AddEventHandler("JustHeist:checkifstarted", function()
    TriggerClientEvent("JustHeist:loadifstarted", source, heist)
    TriggerClientEvent("JustHeist:loadPolice", source, CountPolice())
end)

RegisterServerEvent("JustRigHeist:stopHeist")
AddEventHandler("JustRigHeist:stopHeist", function()
    heist = {}
    -- Notify HMS that oil rig heist failed/stopped
    TriggerEvent('hms:heistFailed', 'oilrig')
    TriggerEvent('hms:updateCooldown', 'oilrig', os.time(), 1800) -- 30 minute cooldown
end)

RegisterServerEvent("JustRigHeist:pdAnnounce")
AddEventHandler("JustRigHeist:pdAnnounce", function(typeNotif)
    AnnouncePolice(typeNotif)
end)

RegisterServerEvent("JustRigHeist:complete")
AddEventHandler("JustRigHeist:complete", function()
    heist = {}

    -- Give items and track rewards
    if Settings.HeistNotifications.Success.Use then
        local rewards = {}
        for i = 1, #Settings.Items do
            AddItem(source, Settings.Items[i].name, Settings.Items[i].count)
            table.insert(rewards, {
                stat = Settings.Items[i].label or Settings.Items[i].name,
                value = "~g~" .. Settings.Items[i].count .. "x"
            })
        end

        -- Show heist success with actual rewards
        TriggerClientEvent('heist-notify:show', source, Settings.HeistNotifications.Success, rewards, 7, true)
    else
        -- Just give items without notification
        for i = 1, #Settings.Items do
            AddItem(source, Settings.Items[i].name, Settings.Items[i].count)
        end
    end

    -- Notify HMS that oil rig heist completed
    TriggerEvent('hms:heistEnded', 'oilrig')
    TriggerEvent('hms:updateCooldown', 'oilrig', os.time(), 1800) -- 30 minute cooldown
end)