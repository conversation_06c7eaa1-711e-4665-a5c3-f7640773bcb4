@keyframes slide-in-bottom {
  0% {
    transform: translateY(200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes slide-in-top {
  0% {
    transform: translateY(-200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes bounce-in-top {
  0% {
    transform: translateY(-500px);
    animation-timing-function: ease-in;
    opacity: 0;
  }
  38% {
    transform: translateY(0);
    animation-timing-function: ease-out;
    opacity: 1;
  }
  55% {
    transform: translateY(-65px);
    animation-timing-function: ease-in;
  }
  72%,
  90%,
  to {
    transform: translateY(0);
    animation-timing-function: ease-out;
  }
  81% {
    transform: translateY(-28px);
    animation-timing-function: ease-in;
  }
  95% {
    transform: translateY(-8px);
    animation-timing-function: ease-in;
  }
}
@keyframes shake-horizontal {
  0%,
  to {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70% {
    transform: translateX(-10px);
  }
  20%,
  40%,
  60% {
    transform: translateX(10px);
  }
  80% {
    transform: translateX(8px);
  }
  90% {
    transform: translateX(-8px);
  }
}
@keyframes roll-in-left {
  0% {
    transform: translateX(-50px) rotate(-540deg);
    opacity: 0;
  }
  to {
    transform: translateX(0) rotate(0deg);
    opacity: 1;
  }
}
@keyframes rotate-90-cw {
  0% {
    transform: rotate(0);
  }
  to {
    transform: rotate(90deg);
  }
}
@keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes custom-animation {
  0% {
    opacity: 0.8;
    transform: scale(0.2);
  }
  80% {
    opacity: 0;
    transform: scale(1.2);
  }
  to {
    opacity: 0;
    transform: scale(2.2);
  }
}
.div,
.image-11-icon {
  position: absolute;
  top: 0;
}
.image-11-icon {
  left: -981px;
  width: 3016px;
  height: 1692px;
  object-fit: cover;
  display: none;
}
.div {
  left: 0;
  width: 1925px;
  height: 1083px;
  mix-blend-mode: screen;
}
.image-12-icon {
  position: absolute;
  top: 14px;
  left: 1px;
  width: 1920px;
  height: 1080px;
  object-fit: cover;
  display: none;
}
.busniess-web-banner-02sds-1-icon {
  position: absolute;
  top: -476.9px;
  left: -961px;
  width: 2899.5px;
  height: 1631.8px;
  object-fit: cover;
}
.divercrellipse-1961 {
  position: absolute;
  top: 428px;
  left: 1194px;
  border-radius: 50%;
  background-color: var(--color-darkgray-100);
  filter: blur(250px);
  width: 841px;
  height: 841px;
}
.divercrshevector-icon,
.vectordivercr4-icon,
.vectordivercrshaep2-icon {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 0;
}
.vectordivercr4-icon {
  height: 58.06%;
  width: 100%;
  top: 25.41%;
  right: 0;
  bottom: 16.53%;
  left: 0;
  object-fit: contain;
}
.vectordivercr4-icon.animate {
  animation: 1s ease 0s 1 normal forwards slide-in-bottom;
}
.divercrshevector-icon,
.vectordivercrshaep2-icon {
  height: 52.46%;
  width: 99.44%;
  top: 28.96%;
  right: 0.45%;
  bottom: 18.57%;
  left: 0.11%;
}
.vectordivercrshaep2-icon.animate {
  animation: 1s ease 1s 1 normal forwards slide-in-bottom;
}
.divercrshevector-icon {
  height: 42.76%;
  width: 99.38%;
  top: 38.66%;
  right: 0.5%;
}
.divercrshevector-icon.animate {
  animation: 1s ease 2s 1 normal forwards slide-in-bottom;
}
.divercrtext-icon {
  position: absolute;
  top: 0;
  left: 1472.2px;
  width: 356.8px;
  height: 1463.9px;
  opacity: 0;
}
.divercrtext-icon.animate {
  animation: 9s ease 2s 1 normal forwards slide-in-top;
}
.divercrgroup-48096143 {
  position: absolute;
  height: 135.55%;
  width: 100.62%;
  top: -10.28%;
  right: -0.51%;
  bottom: -25.27%;
  left: -0.11%;
}
.groupdivercr7-icon,
.vectordivercrdivercrnnn-icon {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.vectordivercrdivercrnnn-icon {
  height: 5.11%;
  width: 4.34%;
  top: 71.04%;
  right: 41.9%;
  bottom: 23.85%;
  left: 53.76%;
  opacity: 0;
}
.vectordivercrdivercrnnn-icon.animate {
  animation: 1s ease-in-out 6s infinite alternate-reverse forwards bounce-in-top;
}
.groupdivercr7-icon {
  height: 22.57%;
  width: 2.68%;
  top: 34.58%;
  right: 3.44%;
  bottom: 42.85%;
  left: 93.88%;
}
.divercr909-icon {
  height: 21.11%;
  width: 0.97%;
  top: 0;
  right: 0;
  bottom: 78.89%;
  left: 99.03%;
  object-fit: contain;
}
.divercr909-icon.animate,
.groupdivercrasdasdf-icon.animate {
  animation: 1s ease-in-out 0s infinite alternate-reverse both slide-in-top;
}
.divercr909-icon,
.groupdivercr45d-icon,
.groupdivercrasdasdf-icon {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 1;
}
.groupdivercrasdasdf-icon {
  height: 21.11%;
  width: 0.97%;
  top: 23.09%;
  right: 99.03%;
  bottom: 55.79%;
  left: 0;
  object-fit: contain;
}
.groupdivercr45d-icon {
  height: 1.4%;
  width: 16.02%;
  top: 98.6%;
  right: 83.02%;
  bottom: 0;
  left: 0.96%;
}
.groupdivercr45d-icon.animate {
  animation: 6s ease 0s infinite normal forwards shake-horizontal;
}
.groupdivercr6 {
  position: absolute;
  height: 79.75%;
  width: 93%;
  top: 2.71%;
  right: 3.43%;
  bottom: 17.54%;
  left: 3.57%;
}
.groupdivercr5-icon {
  position: absolute;
  height: 2.12%;
  width: 1.79%;
  top: 15.21%;
  right: 6.63%;
  bottom: 82.67%;
  left: 91.58%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 1;
}
.groupdivercr5-icon.animate {
  animation: 1s ease-in-out 0s 1 alternate-reverse both roll-in-left;
}
.divercr4-icon,
.groupdivercr2-icon,
.groupdivercr3divercrdfas-icon {
  position: absolute;
  height: 2.11%;
  width: 1.79%;
  top: 76.15%;
  right: 21.8%;
  bottom: 21.73%;
  left: 76.4%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.groupdivercr2-icon,
.groupdivercr3divercrdfas-icon {
  top: 45.88%;
  right: 89.73%;
  bottom: 52.01%;
  left: 8.48%;
  opacity: 1;
}
.groupdivercr3divercrdfas-icon.animate {
  animation: 3s ease 0s infinite alternate-reverse both roll-in-left;
}
.groupdivercr2-icon {
  height: 4.04%;
  width: 3.43%;
  top: 24.19%;
  right: 78.86%;
  bottom: 71.77%;
  left: 17.7%;
}
.groupdivercr2-icon.animate {
  animation: 1s ease 0s infinite normal forwards rotate-90-cw;
}
.groupdivercr-icon {
  height: 4.38%;
  width: 3.72%;
  top: 44.81%;
  right: 16.24%;
  bottom: 50.81%;
  left: 80.04%;
  opacity: 1;
}
.groupdivercr-icon.animate {
  animation: 1s ease-in-out 0s infinite alternate-reverse forwards rotate-center;
}
.groupdivercr-icon,
.vectordivercr-icon,
.vectordivercr-icon1 {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.vectordivercr-icon {
  height: 4.96%;
  width: 4.21%;
  top: 77.21%;
  right: 27.49%;
  bottom: 17.83%;
  left: 68.3%;
  opacity: 0;
}
.vectordivercr-icon.animate {
  animation: 1s ease 0s infinite normal forwards custom-animation;
}
.vectordivercr-icon1 {
  height: 2.8%;
  width: 2.38%;
  top: 80.45%;
  right: 64.27%;
  bottom: 16.74%;
  left: 33.35%;
}
.selectedv {
  position: absolute;
  top: 76.15%;
  left: 4.46%;
  letter-spacing: -0.04em;
  text-transform: uppercase;
  font-weight: 800;
}
.framedivercr {
  position: absolute;
  top: -33px;
  left: -44px;
  width: 1568px;
  height: 1330.2px;
  overflow: hidden;
}
.divercr {
  width: 100%;
  position: relative;
  height: 1080px;
  overflow: hidden;
  text-align: center;
  font-size: var(--font-size-29xl-1);
  color: var(--color-white);
  font-family: var(--font-big-shoulders-display);
}
