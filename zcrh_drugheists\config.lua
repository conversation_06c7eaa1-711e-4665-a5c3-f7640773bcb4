Config = {}
Config.Locale = 'en'

Config.Marker = {
	r = 140, g = 234, b = 255, a = 100,  -- red color
	x = 1.0, y = 1.0, z = 1.0,       -- tiny, cylinder formed circle
	DrawDistance = 15.0, Type = 42    -- default circle type, low draw distance due to indoors area
}

Config.PoliceNumberRequired = 5
Config.TimerBeforeNewRob    = 3600 -- The cooldown timer on a store after robbery was completed / canceled, in seconds

Config.MaxDistance    = 30   -- max distance from the robbary, going any longer away from it will to cancel the robbary
Config.GiveBlackMoney = true -- give black money? If disabled it will give cash instead

-- Heist Notification Configuration
Config.HeistNotifications = {
    Success = {
        Use = true,
        missionTextLabel = "~g~DRUG HEIST~s~",
        passFailTextLabel = "COMPLETED.",
        messageLabel = "Handle the cops and escape!"
    },
    Failed = {
        Use = true,
        missionTextLabel = "~g~DRUG HEIST~s~",
        passFailTextLabel = "FAILED.",
        messageLabel = "You left the area too early."
    }
}

Stores = {
	-- ['paleto_slaughterhouse'] = {
	-- 	position = vector3(-101.7314, 6208.8281, 31.0250),
	-- 	reward = 'inside-weed',
	-- 	nameOfStore = 'Paleto Slaughterhouse',
	-- 	secondsRemaining = 360, -- seconds
	-- 	lastRobbed = 0
	-- },
	-- ['humane_labs'] = {
	-- 	position = vector3(3537.0454, 3662.8445, 28.1219),
	-- 	reward = 'inside-meth',
	-- 	nameOfStore = 'Humane Labs',
	-- 	secondsRemaining = 240, -- seconds
	-- 	lastRobbed = 0
	-- },
	['raven_slaughterhouse'] = {
		position = vector3(974.4865, -2167.2034, 29.4614),
		reward = 'inside-coke',
		nameOfStore = 'Raven Slaughterhouse',
		secondsRemaining = 180, -- seconds
		lastRobbed = 0
	}
}
