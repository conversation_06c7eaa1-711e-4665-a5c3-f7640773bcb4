local lastRob = 0
ESX, QBCore = nil
Citizen.CreateThread(function()
    if Config['YachtHeist']['framework']['name'] == 'ESX' then
        pcall(function() ESX = exports[Config['YachtHeist']['framework']['scriptName']]:getSharedObject() end)
        if not ESX then
            TriggerEvent(Config['YachtHeist']['framework']['eventName'], function(library) 
                ESX = library 
            end)
        end
        ESX.RegisterServerCallback('yachtheist:server:checkPoliceCount', function(source, cb)
            local policeCount = #ESX.GetExtendedPlayers('job', 'police')
        
            if policeCount >= Config['YachtHeist']['requiredPoliceCount'] then
                cb({status = true})
            else
                cb({status = false})
                TriggerClientEvent('yachtheist:client:showNotification', source, Strings['need_police'])
            end
        end)
        ESX.RegisterServerCallback('yachtheist:server:checkTime', function(source, cb, index)
            local src = source
            local player = ESX.GetPlayerFromId(src)
            
            if (os.time() - lastRob) < Config['YachtHeist']['nextRob'] and lastRob ~= 0 then
                local seconds = Config['YachtHeist']['nextRob'] - (os.time() - lastRob)
                TriggerClientEvent('yachtheist:client:showNotification', src, Strings['wait_nextrob'] .. ' ' .. math.floor(seconds / 60) .. ' ' .. Strings['minute'])
                cb({status = false})
            else
                lastRob = os.time()
                discordLog(player.getName() ..  ' - ' .. player.getIdentifier(), ' started the Yacht Heist!')
                cb({status = true})
            end
        end)
        ESX.RegisterServerCallback('yachtheist:server:hasItem', function(source, cb, data)
            local src = source
            local player = ESX.GetPlayerFromId(src)
            local playerItem = player.getInventoryItem(data.itemName)
        
            if player and playerItem ~= nil then
                if playerItem.count >= 1 then
                    cb({status = true, label = playerItem.label})
                else
                    cb({status = false, label = playerItem.label})
                end
            else
                print('[rm_yachtheist] you need add required items to server database')
            end
        end)
    elseif Config['YachtHeist']['framework']['name'] == 'QB' then
        while not QBCore do
            pcall(function() QBCore =  exports[Config['YachtHeist']['framework']['scriptName']]:GetCoreObject() end)
            if not QBCore then
                pcall(function() QBCore =  exports[Config['YachtHeist']['framework']['scriptName']]:GetSharedObject() end)
            end
            if not QBCore then
                TriggerEvent(Config['YachtHeist']['framework']['eventName'], function(obj) QBCore = obj end)
            end
            Citizen.Wait(1)
        end
        QBCore.Functions.CreateCallback('yachtheist:server:checkPoliceCount', function(source, cb)
            local src = source
            local players = QBCore.Functions.GetPlayers()
            local policeCount = 0
        
            for i = 1, #players do
                local player = QBCore.Functions.GetPlayer(players[i])
                if player then
                    for k, v in pairs(Config['YachtHeist']['dispatchJobs']) do
                        if player.PlayerData.job.name == v then
                            policeCount = policeCount + 1
                        end
                    end
                end
            end
        
            if policeCount >= Config['YachtHeist']['requiredPoliceCount'] then
                cb({status = true})
            else
                cb({status = false})
                TriggerClientEvent('yachtheist:client:showNotification', src, Strings['need_police'])
            end
        end)
        QBCore.Functions.CreateCallback('yachtheist:server:checkTime', function(source, cb, index)
            local src = source
            local player = QBCore.Functions.GetPlayer(src)

            if (os.time() - lastRob) < Config['YachtHeist']['nextRob'] and lastRob ~= 0 then
                local seconds = Config['YachtHeist']['nextRob'] - (os.time() - lastRob)
                TriggerClientEvent('yachtheist:client:showNotification', src, Strings['wait_nextrob'] .. ' ' .. math.floor(seconds / 60) .. ' ' .. Strings['minute'])
                cb({status = false})
            else
                lastRob = os.time()
                discordLog(player.PlayerData.name ..  ' - ' .. player.PlayerData.license, ' started the Yacht Heist!')
                cb({status = true})
            end
        end)
        QBCore.Functions.CreateCallback('yachtheist:server:hasItem', function(source, cb, data)
            local src = source
            local player = QBCore.Functions.GetPlayer(src)
            local playerItem = player.Functions.GetItemByName(data.itemName)
        
            if player then 
                if playerItem ~= nil then
                    if playerItem.amount >= 1 then
                        cb({status = true, label = data.itemName})
                    end
                else
                    cb({status = false, label = data.itemName})
                end
            end
        end)
    end
end)

RegisterServerEvent('yachtheist:server:removeItem')
AddEventHandler('yachtheist:server:removeItem', function(item)
    local src = source
    if Config['YachtHeist']['framework']['name'] == 'ESX' then
        local player = ESX.GetPlayerFromId(src)
        if player then
            player.removeInventoryItem(item, 1)
        end
    elseif Config['YachtHeist']['framework']['name'] == 'QB' then
        local player = QBCore.Functions.GetPlayer(src)
        if player then
            player.Functions.RemoveItem(item, 1)
        end 
    end
end)

RegisterServerEvent('yachtheist:server:rewardItem')
AddEventHandler('yachtheist:server:rewardItem', function(item, count, type)
    local src = source

    if Config['YachtHeist']['framework']['name'] == 'ESX' then
        local player = ESX.GetPlayerFromId(src)
        local whitelistItems = {}

        if player then
            if type == 'money' then
                local sourcePed = GetPlayerPed(src)
                local sourceCoords = GetEntityCoords(sourcePed)
                local dist = #(sourceCoords - Config['YachtSetup']['yachtPos'])
                if dist > 100.0 then
                    print('[rm_yachtheist] add money exploit playerID: '.. src .. ' name: ' .. GetPlayerName(src))
                else
                    if Config['YachtHeist']['black_money'] then
                        player.addAccountMoney('black_money', count)
                        discordLog(player.getName() ..  ' - ' .. player.getIdentifier(), ' Gain ' .. count .. '$ on Yacht Heist!')
                    else
                        player.addMoney(count)
                        discordLog(player.getName() ..  ' - ' .. player.getIdentifier(), ' Gain ' .. count .. '$ on Yacht Heist!')
                    end
                end
            else
                for k, v in pairs(Config['YachtHeist']['rewardItems']) do
                    whitelistItems[v['itemName']] = true
                end

                for k, v in pairs(Config['YachtSetup']['artifacts']) do
                    whitelistItems[v['itemName']] = true
                end

                if whitelistItems[item] then
                    if count then 
                        player.addInventoryItem(item, count)
                        discordLog(player.getName() ..  ' - ' .. player.getIdentifier(), ' Gain ' .. item .. ' x' .. count .. ' on Yacht Heist!')
                    else
                        player.addInventoryItem(item, 1)
                        discordLog(player.getName() ..  ' - ' .. player.getIdentifier(), ' Gain ' .. item .. ' x' .. 1 .. ' on Yacht Heist!')
                    end
                else
                    print('[rm_yachtheist] add item exploit playerID: '.. src .. ' name: ' .. GetPlayerName(src))
                end
            end
        end
    elseif Config['YachtHeist']['framework']['name'] == 'QB' then
        local player = QBCore.Functions.GetPlayer(src)
        local whitelistItems = {}

        if player then
            if type == 'money' then
                local sourcePed = GetPlayerPed(src)
                local sourceCoords = GetEntityCoords(sourcePed)
                local dist = #(sourceCoords - Config['YachtSetup']['yachtPos'])
                if dist > 100.0 then
                    print('[rm_yachtheist] add money exploit playerID: '.. src .. ' name: ' .. GetPlayerName(src))
                else
                    if Config['YachtHeist']['black_money'] then
                        local info = {
                            worth = count
                        }
                        player.Functions.AddItem('markedbills', 1, false, info)
                        TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items['markedbills'], "add") 
                        discordLog(player.PlayerData.name ..  ' - ' .. player.PlayerData.license, ' Gain ' .. count .. '$ on Yacht Heist!')
                    else
                        player.Functions.AddMoney('cash', count)
                        discordLog(player.PlayerData.name ..  ' - ' .. player.PlayerData.license, ' Gain ' .. count .. '$ on Yacht Heist!')
                    end
                end
            else
                for k, v in pairs(Config['YachtHeist']['rewardItems']) do
                    whitelistItems[v['itemName']] = true
                end

                for k, v in pairs(Config['YachtSetup']['artifacts']) do
                    whitelistItems[v['itemName']] = true
                end

                if whitelistItems[item] then
                    if count then 
                        player.Functions.AddItem(item, count)
                        discordLog(player.PlayerData.name ..  ' - ' .. player.PlayerData.license, ' Gain ' .. item .. ' x' .. count .. ' on Yacht Heist!')
                    else
                        player.Functions.AddItem(item, 1)
                        discordLog(player.PlayerData.name ..  ' - ' .. player.PlayerData.license, ' Gain ' .. item .. ' x' .. 1 .. ' on Yacht Heist!')
                    end
                else
                    print('[rm_yachtheist] add item exploit playerID: '.. src .. ' name: ' .. GetPlayerName(src))
                end
            end
        end
    end
end)

-- RegisterServerEvent('yachtheist:server:sellRewardItems')
-- AddEventHandler('yachtheist:server:sellRewardItems', function()
--     local src = source

--     if Config['YachtHeist']['framework']['name'] == 'ESX' then
--         local player = ESX.GetPlayerFromId(src)
--         local totalMoney = 0

--         if player then
--             for k, v in pairs(Config['YachtHeist']['rewardItems']) do
--                 local playerItem = player.getInventoryItem(v['itemName'])
--                 if playerItem.count >= 1 then
--                     player.removeInventoryItem(v['itemName'], playerItem.count)
--                     if Config['YachtHeist']['black_money'] then
--                         player.addAccountMoney('black_money', playerItem.count * v['sellPrice'])
--                     else
--                         if Config['YachtHeist']['moneyItem']['status'] then
--                             player.addInventoryItem(Config['YachtHeist']['moneyItem']['itemName'], playerItem.count * v['sellPrice'])
--                         else
--                             player.addMoney(playerItem.count * v['sellPrice'])
--                         end
--                     end
--                     totalMoney = totalMoney + (playerItem.count * v['sellPrice'])
--                 end
--             end

--             for k, v in pairs(Config['YachtSetup']['artifacts']) do
--                 local playerItem = player.getInventoryItem(v['itemName'])
--                 if playerItem.count >= 1 then
--                     player.removeInventoryItem(v['itemName'], playerItem.count)
--                     if Config['YachtHeist']['black_money'] then
--                         player.addAccountMoney('black_money', playerItem.count * v['sellPrice'])
--                     else
--                         if Config['YachtHeist']['moneyItem']['status'] then
--                             player.addInventoryItem(Config['YachtHeist']['moneyItem']['itemName'], playerItem.count * v['sellPrice'])
--                         else
--                             player.addMoney(playerItem.count * v['sellPrice'])
--                         end
--                     end
--                     totalMoney = totalMoney + (playerItem.count * v['sellPrice'])
--                 end
--             end

--             discordLog(player.getName() ..  ' - ' .. player.getIdentifier(), ' Gain $' .. math.floor(totalMoney) .. ' on the Union Heist Buyer!')
--             TriggerClientEvent('yachtheist:client:showNotification', src, Strings['total_money'] .. ' $' .. math.floor(totalMoney))
--         end
--     elseif Config['YachtHeist']['framework']['name'] == 'QB' then
--         local player = QBCore.Functions.GetPlayer(src)
--         local totalMoney = 0

--         if player then
--             for k, v in pairs(Config['YachtHeist']['rewardItems']) do
--                 local playerItem = player.Functions.GetItemByName(v['itemName'])
--                 if playerItem ~= nil and playerItem.amount > 0 then
--                     player.Functions.RemoveItem(v['itemName'], playerItem.amount)
--                     if Config['YachtHeist']['black_money'] then
--                         local info = {
--                             worth = playerItem.amount * v['sellPrice']
--                         }
--                         player.Functions.AddItem('markedbills', 1, false, info)
--                         TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items['markedbills'], "add") 
--                     else
--                         player.Functions.AddMoney('cash', playerItem.amount * v['sellPrice'])
--                     end
--                     totalMoney = totalMoney + (playerItem.amount * v['sellPrice'])
--                 end
--             end

--             for k, v in pairs(Config['YachtSetup']['artifacts']) do
--                 local playerItem = player.Functions.GetItemByName(v['itemName'])
--                 if playerItem ~= nil and playerItem.amount > 0 then
--                     player.Functions.RemoveItem(v['itemName'], playerItem.amount)
--                     if Config['YachtHeist']['black_money'] then
--                         local info = {
--                             worth = playerItem.amount * v['sellPrice']
--                         }
--                         player.Functions.AddItem('markedbills', 1, false, info)
--                         TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items['markedbills'], "add") 
--                     else
--                         player.Functions.AddMoney('cash', playerItem.amount * v['sellPrice'])
--                     end
--                     totalMoney = totalMoney + (playerItem.amount * v['sellPrice'])
--                 end
--             end

--             discordLog(player.PlayerData.name ..  ' - ' .. player.PlayerData.license, ' Gain $' .. math.floor(totalMoney) .. ' on the Union Heist Buyer!')
--             TriggerClientEvent('yachtheist:client:showNotification', src, Strings['total_money'] .. ' $' .. math.floor(totalMoney))
--         end
--     end
-- end)

RegisterCommand('pdyacht', function(source, args)
    local src = source

    if Config['YachtHeist']['framework']['name'] == 'ESX' then
        local player = ESX.GetPlayerFromId(src)

        if player then
            if player['job']['name'] == Config['YachtHeist']['dispatchJobs'][1] then
                return TriggerClientEvent('yachtheist:client:sync', -1, 'resetHeist')
            end

            TriggerClientEvent('yachtheist:client:showNotification', src, Strings['not_cop'])
        end
    elseif Config['YachtHeist']['framework']['name'] == 'QB' then
        local player = QBCore.Functions.GetPlayer(src)
    
        if player then
            for k, v in pairs(Config['YachtHeist']['dispatchJobs']) do
                if player.PlayerData.job.name == v then
                    return TriggerClientEvent('yachtheist:client:sync', -1, 'resetHeist')
                end
            end

            TriggerClientEvent('yachtheist:client:showNotification', src, Strings['not_cop'])
        end
    end
end)

RegisterServerEvent('yachtheist:server:sync')
AddEventHandler('yachtheist:server:sync', function(type, args)
    TriggerClientEvent('yachtheist:client:sync', -1, type, args)
end)