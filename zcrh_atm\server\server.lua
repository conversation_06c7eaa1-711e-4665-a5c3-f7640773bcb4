ESX = exports["es_extended"]:getSharedObject()

RegisterServerEvent("zcrh_atm:server:removeitem")
AddEventHandler("zcrh_atm:server:removeitem", function(data)
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)
	local item = data
	xPlayer.removeInventoryItem(item, 1)
end)

RegisterServerEvent("zcrh_atm:server:hackfailed")
AddEventHandler("zcrh_atm:server:hackfailed", function()
	-- Notify HMS that ATM heist failed
	TriggerEvent('hms:heistFailed', 'atmrobbery')
	TriggerEvent('hms:updateCooldown', 'atmrobbery', os.time(), 20) -- 20 second cooldown even on failure
end)

RegisterNetEvent('zcrh_atm:server:givecash')
AddEventHandler('zcrh_atm:server:givecash', function()
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)
    local randomMoney = math.random(Config.MinWithdrawl ,Config.MaxWithdrawal)
	xPlayer.showNotification("ATM cash received: $" .. randomMoney)
	xPlayer.addAccountMoney(Config.Account, randomMoney)

	-- Show heist success with actual amount
	if Config.HeistNotifications.Success.Use then
		local rewards = {
			{
				stat = "Cash Withdrawn",
				value = "~g~$" .. randomMoney
			}
		}
		TriggerClientEvent('heist-notify:show', src, Config.HeistNotifications.Success, rewards, 7, true)
	end

	-- Notify HMS that ATM heist ended successfully
	TriggerEvent('hms:heistEnded', 'atmrobbery')
	-- Note: ATM has no cooldown as per config
end)


ESX.RegisterServerCallback('zcrh_atm:server:hasitem', function(source, cb, item)
    local xPlayer = ESX.GetPlayerFromId(source)
    local hasItem = false
    if xPlayer.getInventoryItem(item).count > 0 then
        hasItem = true
    end
    cb(hasItem)
end)

ESX.RegisterServerCallback('zcrh_atm:server:checkPolice', function(source, cb)
    local policeOnline = #ESX.GetExtendedPlayers('job', Config.CopsJobs[1])
    if policeOnline >= Config.RequiredCopsCount then
        cb(true)
    else
        cb(false)
    end
end)
