@keyframes slide-in-bottom {
  0% {
    transform: translateY(200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes slide-in-top {
  0% {
    transform: translateY(-200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes custom-animation {
  0% {
    opacity: 0.8;
    transform: scale(0.2);
  }
  80% {
    opacity: 0;
    transform: scale(1.2);
  }
  to {
    opacity: 0;
    transform: scale(2.2);
  }
}
@keyframes slide-in-left {
  0% {
    transform: translateX(-200px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.image-11-icon {
  position: absolute;
  top: 0;
  left: -981px;
  width: 3016px;
  height: 1692px;
  object-fit: cover;
  display: none;
}
.bomberell1 {
  position: absolute;
  top: 428px;
  left: 1194px;
  border-radius: 50%;
  background-color: #aeaeae;
  filter: blur(250px);
  width: 841px;
  height: 841px;
}
.bombernoic-icon,
.image-12-icon {
  position: absolute;
  object-fit: cover;
}
.image-12-icon {
  top: 14px;
  left: 1px;
  width: 1920px;
  height: 1080px;
  display: none;
}
.bombernoic-icon {
  top: -476.9px;
  left: -961px;
  width: 2899.5px;
  height: 1631.8px;
}
.bomber11-icon,
.bomber2-icon,
.bomber33-icon {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 0;
}
.bomber33-icon {
  height: 77.13%;
  width: 100.41%;
  top: 24.17%;
  right: -0.3%;
  bottom: -1.3%;
  left: -0.1%;
  object-fit: contain;
}
.bomber33-icon.animate {
  animation: 1s ease 0s 1 normal forwards slide-in-bottom;
}
.bomber11-icon,
.bomber2-icon {
  height: 71.11%;
  width: 99.9%;
  top: 28.98%;
  right: 0.1%;
  bottom: -0.09%;
  left: 0;
}
.bomber2-icon.animate {
  animation: 1s ease 1s 1 normal forwards slide-in-bottom;
}
.bomber11-icon {
  height: 57.96%;
  top: 42.13%;
}
.bomber11-icon.animate {
  animation: 1s ease 2s 1 normal forwards slide-in-bottom;
}
.vectorbomber-icon {
  position: absolute;
  top: -107.7px;
  left: 1480.8px;
  width: 385.9px;
  height: 1364.3px;
  opacity: 0;
}
.vectorbomber-icon.animate {
  animation: 9s ease 3s 1 normal forwards slide-in-top;
}
.bomber2-icon1 {
  position: absolute;
  height: 6.3%;
  width: 3.54%;
  top: 84.26%;
  right: 54.48%;
  bottom: 9.44%;
  left: 41.98%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.bomber-icon,
.bomber1-icon,
.bomber3-icon,
.bomber4-icon,
.bomber8-icon {
  position: absolute;
  height: 27.8%;
  width: 2.19%;
  top: 39.35%;
  right: 24.48%;
  bottom: 32.85%;
  left: 73.33%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 0;
}
.bomber8-icon.animate {
  animation: 1s ease 2s 1 normal forwards slide-in-top;
}
.bomber-icon,
.bomber1-icon,
.bomber3-icon,
.bomber4-icon {
  height: 2.6%;
  width: 1.46%;
  top: 90.56%;
  right: 38.07%;
  bottom: 6.84%;
  left: 60.47%;
  opacity: 1;
}
.bomber-icon.animate {
  animation: 1s ease 0s infinite normal forwards rotate-center;
}
.bomber1-icon,
.bomber3-icon,
.bomber4-icon {
  top: 53.27%;
  right: 93.54%;
  bottom: 44.13%;
  left: 5%;
}
.bomber3-icon.animate,
.bomber4-icon.animate {
  animation: 2s ease 0s infinite normal forwards rotate-center;
}
.bomber1-icon,
.bomber3-icon {
  height: 4.97%;
  width: 2.8%;
  top: 26.56%;
  right: 84.67%;
  bottom: 68.47%;
  left: 12.53%;
}
.bomber1-icon {
  height: 6.11%;
  width: 3.44%;
  top: 91.85%;
  right: 42.71%;
  bottom: 2.04%;
  left: 53.85%;
  opacity: 0;
}
.bomber1-icon.animate {
  animation: 1s ease 0s infinite normal forwards custom-animation;
}
.bomberselected-icon,
.bombervector1-icon,
.hebomberd-icon,
.vebomberd-icon {
  position: absolute;
  height: 3.45%;
  width: 1.94%;
  top: 95.85%;
  right: 72.74%;
  bottom: 0.69%;
  left: 25.31%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.bomberselected-icon,
.hebomberd-icon,
.vebomberd-icon {
  height: 3.64%;
  width: 7.28%;
  top: 91.4%;
  right: 90.93%;
  bottom: 4.96%;
  left: 1.79%;
}
.hebomberd-icon,
.vebomberd-icon {
  height: 20.74%;
  width: 0.74%;
  top: 8.33%;
  right: 96.29%;
  bottom: 70.93%;
  left: 2.97%;
  opacity: 1;
}
.hebomberd-icon.animate {
  animation: 1s ease-in-out 3s infinite alternate-reverse none slide-in-top;
}
.vebomberd-icon {
  height: 1.38%;
  width: 12.17%;
  top: 96.94%;
  right: 86.06%;
  bottom: 1.68%;
  left: 1.77%;
}
.vebomberd-icon.animate {
  animation: 1s ease-in-out 3s infinite alternate-reverse both slide-in-left;
}
.bomber {
  width: 100%;
  position: relative;
  height: 1080px;
  overflow: hidden;
}
