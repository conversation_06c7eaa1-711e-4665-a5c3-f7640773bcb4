
ESX = exports["es_extended"]:getSharedObject()
function notification(title, text, time, type)
    ESX.ShowNotification(text, time, type)
end

RegisterNetEvent('zcrh_shop:client:DefaultNotify')
AddEventHandler('zcrh_shop:client:DefaultNotify', function(text)
    ESX.ShowNotification(text)
end)

function PoliceAlertNotify(coords, ShopName)
    -- Police notifications handled by cd_dispatch
end

function PlayerDied()
    local died = false
    if (GetEntityHealth(PlayerPedId()) <= PlayerDiedHealth) then
        died = true
    end
    return died
end

function NoCarryWeapon()
    if GetSelectedPedWeapon(PlayerPedId()) == GetHashKey('WEAPON_UNARMED') then
        return true
    else
        SendNotify(18)
        return false
    end
end

function SendNotify(Number)
    notification(Config.Notify[Number][1], Config.Notify[Number][2], Config.Notify[Number][3], Config.Notify[Number][4])
end

function loadModel(model)
    if type(model) == 'number' then
        model = model
    else
        model = GetHashKey(model)
    end
    while not HasModelLoaded(model) do
        RequestModel(model)
        Citizen.Wait(0)
    end
end

function HelpNotify(text)
    AddTextEntry('HelpNotification', text)
    BeginTextCommandDisplayHelp('HelpNotification')
    EndTextCommandDisplayHelp(0, false)
end

function SpinnerFunction(text, time)
    -- Simple spinner implementation
    Citizen.CreateThread(function()
        local endTime = GetGameTimer() + time
        while GetGameTimer() < endTime do
            Citizen.Wait(100)
        end
    end)
end