local holdingup = false
local store = ""
local blipRobbery = nil
local vetrineRotte = 0
local robbingName = ""
local animazione = false
local incircle = false
local soundid = GetSoundId()
local alarmTimer = nil

local vetrine = {
	{x = 147.085, y = -1048.612, z = 29.346, heading = 70.326, isOpen = false},--
	{x = -626.735, y = -238.545, z = 38.057, heading = 214.907, isOpen = false},--
	{x = -625.697, y = -237.877, z = 38.057, heading = 217.311, isOpen = false},--
	{x = -626.825, y = -235.347, z = 38.057, heading = 33.745, isOpen = false},--
	{x = -625.77, y = -234.563, z = 38.057, heading = 33.572, isOpen = false},--
	{x = -627.957, y = -233.918, z = 38.057, heading = 215.214, isOpen = false},--
	{x = -626.971, y = -233.134, z = 38.057, heading = 215.532, isOpen = false},--
	{x = -624.433, y = -231.161, z = 38.057, heading = 305.159, isOpen = false},--
	{x = -623.045, y = -232.969, z = 38.057, heading = 303.496, isOpen = false},--
	{x = -620.265, y = -234.502, z = 38.057, heading = 217.504, isOpen = false},--
	{x = -619.225, y = -233.677, z = 38.057, heading = 213.35, isOpen = false},--
	{x = -620.025, y = -233.354, z = 38.057, heading = 34.18, isOpen = false},--
	{x = -617.487, y = -230.605, z = 38.057, heading = 309.177, isOpen = false},--
	{x = -618.304, y = -229.481, z = 38.057, heading = 304.243, isOpen = false},--
	{x = -619.741, y = -230.32, z = 38.057, heading = 124.283, isOpen = false},--
	{x = -619.686, y = -227.753, z = 38.057, heading = 305.245, isOpen = false},--
	{x = -620.481, y = -226.59, z = 38.057, heading = 304.677, isOpen = false},--
	{x = -621.098, y = -228.495, z = 38.057, heading = 127.046, isOpen = false},--
	{x = -623.855, y = -227.051, z = 38.057, heading = 38.605, isOpen = false},--
	{x = -624.977, y = -227.884, z = 38.057, heading = 48.847, isOpen = false},--
	{x = -624.056, y = -228.228, z = 38.057, heading = 216.443, isOpen = false},--
}

-- ESX is now loaded via imports.lua

function DrawText3D(x, y, z, text, scale)
	local onScreen, _x, _y = World3dToScreen2d(x, y, z)

	SetTextScale(scale, scale)
	SetTextFont(4)
	SetTextProportional(1)
	SetTextEntry("STRING")
	SetTextCentre(1)
	SetTextColour(255, 255, 255, 215)

	AddTextComponentString(text)
	DrawText(_x, _y)

end

function DisplayHelpText(str)
	SetTextComponentFormat("STRING")
	AddTextComponentString(str)
	DisplayHelpTextFromStringLabel(0, 0, 1, -1)
end

RegisterNetEvent("mt:missiontext")
AddEventHandler("mt:missiontext", function(text, time)
    ClearPrints()
    SetTextEntry_2("STRING")
    AddTextComponentString(text)
    DrawSubtitleTimed(time, 1)
end)

local function loadAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        RequestAnimDict(dict)
        Citizen.Wait(5)
    end
end

RegisterNetEvent('zcrh_vangelico:currentlyrobbing')
AddEventHandler('zcrh_vangelico:currentlyrobbing', function(robb)
	holdingup = true
	store = robb
end)

RegisterNetEvent('zcrh_vangelico:killblip')
AddEventHandler('zcrh_vangelico:killblip', function()
    RemoveBlip(blipRobbery)
end)

RegisterNetEvent('zcrh_vangelico:setblip')
AddEventHandler('zcrh_vangelico:setblip', function(position)
    blipRobbery = AddBlipForCoord(position.x, position.y, position.z)
    SetBlipSprite(blipRobbery , 161)
    SetBlipScale(blipRobbery , 2.0)
    SetBlipColour(blipRobbery, 3)
    PulseBlip(blipRobbery)
end)

RegisterNetEvent('zcrh_vangelico:toofarlocal')
AddEventHandler('zcrh_vangelico:toofarlocal', function(robb)
	holdingup = false

	-- Show heist failure notification
	if Config.HeistNotifications.Failed.Use then
		TriggerEvent('heist-notify:show', Config.HeistNotifications.Failed, nil, 5, true)
	else
		ESX.ShowNotification(_U('robbery_cancelled'))
	end

	robbingName = ""
	incircle = false
end)


RegisterNetEvent('zcrh_vangelico:robberycomplete')
AddEventHandler('zcrh_vangelico:robberycomplete', function(robb)
	holdingup = false
	ESX.ShowNotification(_U('robbery_complete'))
	store = ""
	incircle = false
end)

-- Heist failure notification (client-side is fine since no reward data involved)
RegisterNetEvent('zcrh_vangelico:heistFailed')
AddEventHandler('zcrh_vangelico:heistFailed', function()
	if Config.HeistNotifications.Failed.Use then
		TriggerEvent('heist-notify:show', Config.HeistNotifications.Failed, nil, 5, true)
	end
end)

-- Success notifications now handled by server with actual reward data

Citizen.CreateThread(function()
	for k,v in pairs(Stores)do
		local ve = v.position

		local blip = AddBlipForCoord(ve.x, ve.y, ve.z)
		SetBlipSprite(blip, 617)
		SetBlipScale(blip, 0.9)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString(_U('shop_robbery'))
		EndTextCommandSetBlipName(blip)
	end
end)

-- Variables moved to top of file

-- Removed broken clothing dependency

Citizen.CreateThread(function()
      
	while true do
		local pos = GetEntityCoords(PlayerPedId(), true)

		for k,v in pairs(Stores)do
			local pos2 = v.position

			if(Vdist(pos.x, pos.y, pos.z, pos2.x, pos2.y, pos2.z) < 15.0)then
				if not holdingup then
					DrawMarker(27, v.position.x, v.position.y, v.position.z-0.9, 0, 0, 0, 0, 0, 0, 2.001, 2.0001, 0.5001, 255, 0, 0, 200, 0, 0, 0, 0)

					if(Vdist(pos.x, pos.y, pos.z, pos2.x, pos2.y, pos2.z) < 1.0)then
						if (incircle == false) then
							DisplayHelpText(_U('press_to_rob'))
						end
						incircle = true
						if IsPedShooting(PlayerPedId()) then
							ESX.TriggerServerCallback('zcrh_vangelico:getPoliceCount', function(CopsConnected)
								if CopsConnected >= Config.RequiredCopsRob then
									TriggerServerEvent('zcrh_vangelico:rob', k)
									PlaySoundFromCoord(soundid, "VEHICLES_HORNS_AMBULANCE_WARNING", pos2.x, pos2.y, pos2.z)

									-- Stop alarm after 10 seconds
									if alarmTimer then
										alarmTimer = nil -- Clear previous timer reference
									end
									alarmTimer = Citizen.CreateThread(function()
										Citizen.Wait(10000) -- 10 seconds
										StopSound(soundid)
										alarmTimer = nil
									end)
								else
									TriggerEvent('esx:showNotification', _U('min_two_police') .. Config.RequiredCopsRob .. _U('min_two_police2'))
								end
							end)
                        end
					elseif(Vdist(pos.x, pos.y, pos.z, pos2.x, pos2.y, pos2.z) > 1.0)then
						incircle = false
					end		
				end
			end
		end

		if holdingup then
			for _,v in pairs(vetrine) do
				if(GetDistanceBetweenCoords(pos, v.x, v.y, v.z, true) < 10.0) and not v.isOpen and Config.EnableMarker then 
					DrawMarker(20, v.x, v.y, v.z, 0, 0, 0, 0, 0, 0, 0.6, 0.6, 0.6, 0, 255, 0, 200, 1, 1, 0, 0)
				end
				if(GetDistanceBetweenCoords(pos, v.x, v.y, v.z, true) < 0.75) and not v.isOpen then 
					DrawText3D(v.x, v.y, v.z, '~w~[~g~E~w~] ' .. _U('press_to_collect'), 0.6)
					if IsControlJustPressed(0, 38) then
						animazione = true
					    SetEntityCoords(PlayerPedId(), v.x, v.y, v.z-0.95)
					    SetEntityHeading(PlayerPedId(), v.heading)
						v.isOpen = true 
						PlaySoundFromCoord(-1, "Glass_Smash", v.x, v.y, v.z, "", 0, 0, 0)
					    if not HasNamedPtfxAssetLoaded("scr_jewelheist") then
					    RequestNamedPtfxAsset("scr_jewelheist")
					    end
					    while not HasNamedPtfxAssetLoaded("scr_jewelheist") do
					    Citizen.Wait(0)
					    end
					    SetPtfxAssetNextCall("scr_jewelheist")
					    StartParticleFxLoopedAtCoord("scr_jewel_cab_smash", v.x, v.y, v.z, 0.0, 0.0, 0.0, 1.0, false, false, false, false)
					    loadAnimDict( "missheist_jewel" ) 
						TaskPlayAnim(PlayerPedId(), "missheist_jewel", "smash_case", 8.0, 1.0, -1, 2, 0, 0, 0, 0 ) 
						TriggerEvent("mt:missiontext", _U('collectinprogress'), 3000)
					    DrawSubtitleTimed(5000, 1)
					    Citizen.Wait(5000)
					    ClearPedTasksImmediately(PlayerPedId())
					    TriggerServerEvent('zcrh_vangelico:collectJewels')
					    PlaySound(-1, "PICK_UP", "HUD_FRONTEND_DEFAULT_SOUNDSET", 0, 0, 1)
					    vetrineRotte = vetrineRotte+1
					    animazione = false

						if vetrineRotte == Config.MaxWindows then
						    for _,v in pairs(vetrine) do
								v.isOpen = false
								vetrineRotte = 0
							end
							TriggerServerEvent('zcrh_vangelico:endrob', store)
						    -- ESX.ShowNotification(_U('lester'))
						    holdingup = false
						    StopSound(soundid)
						    -- Clear alarm timer when heist ends
						    if alarmTimer then
						    	alarmTimer = nil
						    end
						end
					end
				end	
			end

			local playerCoords = GetEntityCoords(PlayerPedId())
			if (GetDistanceBetweenCoords(playerCoords.x, playerCoords.y, playerCoords.z, -629.8970, -236.5274, 39.6235, true) > 21.5 ) then
				TriggerServerEvent('zcrh_vangelico:toofar', store)
				holdingup = false
				for _,v in pairs(vetrine) do
					v.isOpen = false
					vetrineRotte = 0
				end
				StopSound(soundid)
				-- Clear alarm timer when player leaves area
				if alarmTimer then
					alarmTimer = nil
				end
			end

		end

		Citizen.Wait(0)
	end
end)

Citizen.CreateThread(function()
      
	while true do
		Wait(1)
		if animazione == true then
			if not IsEntityPlayingAnim(PlayerPedId(), 'missheist_jewel', 'smash_case', 3) then
				TaskPlayAnim(PlayerPedId(), 'missheist_jewel', 'smash_case', 8.0, 8.0, -1, 17, 1, false, false, false)
			end
		end
	end
end)

-- Jewel selling system removed - use external selling methods

