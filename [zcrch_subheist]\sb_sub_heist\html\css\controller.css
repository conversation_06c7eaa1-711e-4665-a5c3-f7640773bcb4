@keyframes slide-in-bottom {
  0% {
    transform: translateY(200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes slide-in-top {
  0% {
    transform: translateY(-200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes custom-animation {
  0% {
    opacity: 0.8;
    transform: scale(0.2);
  }
  80% {
    opacity: 0;
    transform: scale(1.2);
  }
  to {
    opacity: 0;
    transform: scale(2.2);
  }
}
@keyframes slide-in-left {
  0% {
    transform: translateX(-200px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.image-11-icon {
  position: absolute;
  top: 0;
  left: -981px;
  width: 3016px;
  height: 1692px;
  object-fit: cover;
  display: none;
}
.ellcontroller {
  position: absolute;
  top: 428px;
  left: 1194px;
  border-radius: 50%;
  background-color: #aeaeae;
  filter: blur(250px);
  width: 841px;
  height: 841px;
}
.image-12-icon {
  position: absolute;
  top: 14px;
  left: 1px;
  width: 1920px;
  height: 1080px;
  object-fit: cover;
  display: none;
}
.controllernoic-icon {
  position: absolute;
  top: -476.9px;
  left: -961px;
  width: 2899.5px;
  height: 1631.8px;
  object-fit: cover;
}
.vectorcontroller3-icon {
  position: absolute;
  height: 77.13%;
  width: 100.41%;
  top: 24.17%;
  right: -0.3%;
  bottom: -1.3%;
  left: -0.1%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  object-fit: contain;
  opacity: 0;
}
.vectorcontroller3-icon.animate {
  animation: 1s ease 0s 1 normal forwards slide-in-bottom;
}
.vectorcontrollershp-icon,
.vectorcontrollersp2-icon {
  position: absolute;
  height: 71.11%;
  width: 100.16%;
  top: 28.98%;
  right: -0.16%;
  bottom: -0.09%;
  left: 0;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 0;
}
.vectorcontrollersp2-icon.animate {
  animation: 1s ease 1s 1 normal forwards slide-in-bottom;
}
.vectorcontrollershp-icon {
  height: 57.96%;
  top: 42.13%;
}
.vectorcontrollershp-icon.animate {
  animation: 1s ease 2s 1 normal forwards slide-in-bottom;
}
.vectorcontroller-icon {
  position: absolute;
  top: -0.3px;
  left: 52.5px;
  width: 511px;
  height: 1214.4px;
  opacity: 0;
}
.vectorcontroller-icon.animate {
  animation: 9s ease 2s 1 normal forwards slide-in-top;
}
.controllerfrmm {
  position: absolute;
  top: -19px;
  left: 1308px;
  width: 612px;
  height: 1214px;
}
.controller-icon,
.controller2-icon,
.controller4-icon,
.controller8-icon {
  position: absolute;
  height: 5.11%;
  width: 4.34%;
  top: 71.04%;
  right: 41.9%;
  bottom: 23.85%;
  left: 53.76%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.controller-icon,
.controller4-icon,
.controller8-icon {
  height: 22.57%;
  width: 2.68%;
  top: 36.24%;
  right: 14.6%;
  bottom: 41.2%;
  left: 82.72%;
  opacity: 0;
}
.controller8-icon.animate {
  animation: 1s ease 2s 1 normal forwards slide-in-top;
}
.controller-icon,
.controller4-icon {
  height: 2.11%;
  width: 1.79%;
  top: 76.15%;
  right: 21.8%;
  bottom: 21.73%;
  left: 76.4%;
  opacity: 1;
}
.controller-icon.animate {
  animation: 1s ease 0s infinite normal forwards rotate-center;
}
.controller4-icon {
  top: 45.88%;
  right: 89.73%;
  bottom: 52.01%;
  left: 8.48%;
}
.controller3-icon.animate,
.controller4-icon.animate {
  animation: 2s ease 0s infinite normal forwards rotate-center;
}
.controller3-icon {
  height: 4.04%;
  width: 3.43%;
  top: 24.19%;
  right: 78.86%;
  bottom: 71.77%;
  left: 17.7%;
  opacity: 1;
}
.controller1-icon,
.controller3-icon,
.controllervector1-icon {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.controller1-icon {
  height: 4.96%;
  width: 4.21%;
  top: 77.21%;
  right: 27.49%;
  bottom: 17.83%;
  left: 68.3%;
  opacity: 0;
}
.controller1-icon.animate {
  animation: 1s ease 0s infinite normal forwards custom-animation;
}
.controllervector1-icon {
  height: 2.8%;
  width: 2.38%;
  top: 80.45%;
  right: 64.27%;
  bottom: 16.74%;
  left: 33.35%;
}
.controllerselected-icon,
.hecontrollerd-icon,
.hecontrollerd-icon1,
.vecontrollerd-icon {
  position: absolute;
  height: 2.95%;
  width: 8.92%;
  top: 76.84%;
  right: 86.53%;
  bottom: 20.21%;
  left: 4.55%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.hecontrollerd-icon,
.hecontrollerd-icon1,
.vecontrollerd-icon {
  height: 16.84%;
  width: 0.91%;
  top: 9.4%;
  right: 93.1%;
  bottom: 73.76%;
  left: 5.99%;
  opacity: 1;
}
.hecontrollerd-icon.animate,
.hecontrollerd-icon1.animate {
  animation: 1s ease-in-out 3s infinite alternate-reverse none slide-in-top;
}
.hecontrollerd-icon1,
.vecontrollerd-icon {
  top: 1.2%;
  right: 14.15%;
  bottom: 81.96%;
  left: 84.95%;
}
.vecontrollerd-icon {
  height: 1.12%;
  width: 14.9%;
  top: 81.34%;
  right: 80.57%;
  bottom: 17.54%;
  left: 4.53%;
}
.vecontrollerd-icon.animate {
  animation: 1s ease-in-out 3s infinite alternate-reverse both slide-in-left;
}
.controllerfr {
  position: absolute;
  top: -38px;
  left: -39px;
  width: 1568px;
  height: 1330.2px;
  overflow: hidden;
}
.controller {
  width: 100%;
  position: relative;
  height: 1080px;
  overflow: hidden;
}
