--RAINMAD SCRIPTS - discord.gg/rccvdkmA5X - rainmad.tebex.io
Config = {}

Config['YachtHeist'] = {
    ['framework'] = {
        name = 'ESX', -- Only ESX or QB.
        scriptName = 'es_extended', -- Only for QB users.
        eventName = 'esx:getSharedObject' -- Only for ESX users.
    },
    ['bagClothesID'] = 45,
    ['buyerFinishScene'] = true,
    ["dispatch"] = "cd_dispatch", -- cd_dispatch | qs-dispatch | ps-dispatch | rcore_dispatch | default
    ['requiredPoliceCount'] = 3, -- required police count for start heist
    ['dispatchJobs'] = {'police'},
    ['nextRob'] = 7200, -- Seconds for next heist.
    ['requiredItems'] = { -- Add this items to database or shared. Don't change the order, you can change the item names.
        'bag'
    },
    ['rewardItems'] = { -- Add this items to database or shared. Don't change the order, you can change the item names.
        {itemName = 'gold',       count = 75, sellPrice = 100}, -- For stacks.
        {itemName = 'coke_pooch', count = 75, sellPrice = 100}, -- For stacks.
        {itemName = 'weed_pooch', count = 75, sellPrice = 100}, -- For stacks.
    },
    ['rewardMoneys'] = {
        ['stacks'] = function()
            return math.random(250000, 350000) -- Per money stacks
        end,
    },
    ['moneyItem'] = { -- If your server have money item, you can set it here.
        status = false,
        itemName = 'cash'
    },
    ['black_money'] = true,  -- If change true, all moneys will convert to black. QBCore players can change itemName.
    ['startHeist'] ={ -- Heist start coords
        pos = vector3(-1612.8820, -1028.5046, 13.1531),
        peds = {
            {pos = vector3(-1612.8820, -1028.5046, 13.1531), heading = 229.5155, ped = 's_m_m_highsec_01'},
        }
    },
    ['finishHeist'] = { -- Heist finish coords.
        buyerPos = vector3(-736.13, -1485.6, 4.00020)
    },
    ['enableIPL'] = true, -- If you dont have any ipl script, you need change to true

    -- Heist Notification Configuration
    ['HeistNotifications'] = {
        Success = {
            Use = true,
            missionTextLabel = "~r~YACHT HEIST~s~",
            passFailTextLabel = "COMPLETED.",
            messageLabel = "Escape before backup arrives!"
        },
        Failed = {
            Use = true,
            missionTextLabel = "~r~YACHT HEIST~s~",
            passFailTextLabel = "FAILED.",
            messageLabel = "Better luck next time!"
        }
    }
}

Config['YachtSetup'] = {
    ['yachtPos'] = vector3(-2087.7, -1024.4, 4.88214),
    ['guards'] = { 
        ['peds'] = {-- These coords are for guard peds, you can add new guard peds.
            {coords = vector3(-2034.4, -1028.1, 5.88201),  heading = 270.87, model = 's_m_y_swat_01'},
            {coords = vector3(-2034.9, -1030.0, 5.88211),  heading = 177.93, model = 's_m_y_swat_01'},
            {coords = vector3(-2038.3, -1040.4, 5.88255),  heading = 354.93, model = 'ig_fbisuit_01'},
            {coords = vector3(-2032.7, -1040.4, 5.88216),  heading = 177.88, model = 's_m_y_swat_01'},
            {coords = vector3(-2049.4, -1036.5, 5.88307),  heading = 268.28, model = 's_m_y_swat_01'},
            {coords = vector3(-2065.6, -1031.2, 5.88297),  heading = 268.3,  model = 's_m_y_swat_01'},
            {coords = vector3(-2046.4, -1023.5, 5.88342),  heading = 359.44, model = 'ig_fbisuit_01'},
            {coords = vector3(-2046.4, -1023.5, 5.88342),  heading = 265.05, model = 's_m_y_swat_01'},
            {coords = vector3(-2075.7, -1021.4, 5.88412),  heading = 174.77, model = 's_m_y_swat_01'},
            {coords = vector3(-2084.9, -1023.9, 5.88345),  heading = 180.79, model = 'ig_fbisuit_01'},
            {coords = vector3(-2095.5, -1019.6, 5.90781),  heading = 180.79, model = 'ig_fbisuit_01'},
            {coords = vector3(-2094.8, -1014.0, 5.88435),  heading = 180.79, model = 's_m_y_swat_01'},
            {coords = vector3(-2088.2, -1015.5, 5.88413),  heading = 180.79, model = 'ig_fbisuit_01'},
            {coords = vector3(-2083.9, -1015.1, 5.88413),  heading = 180.79, model = 's_m_y_swat_01'},
            {coords = vector3(-2118.1, -1005.4, 7.90748),  heading = 180.79, model = 's_m_y_swat_01'},
            {coords = vector3(-2117.9, -1010.7, 7.83777),  heading = 180.79, model = 's_m_y_swat_01'},
            {coords = vector3(-2110.3, -1011.9, 8.96900),  heading = 180.79, model = 's_m_y_swat_01'},
            {coords = vector3(-2108.6, -1008.2, 8.95518),  heading = 180.79, model = 's_m_y_swat_01'},
            {coords = vector3(-2101.2, -1012.6, 8.98279),  heading = 180.79, model = 's_m_y_swat_01'},
            {coords = vector3(-2057.9478, -1020.0406, 5.8820),  heading = 252.1806, model = 's_m_y_swat_01'},
            {coords = vector3(-2074.3303, -1014.8256, 5.8820),  heading = 236.2478, model = 'ig_fbisuit_01'},
            {coords = vector3(-2076.3828, -1025.4633, 5.8841),  heading = 66.3681, model = 's_m_y_swat_01'},
            {coords = vector3(-2078.6887, -1027.0000, 5.8830),  heading = 253.2709, model = 's_m_y_swat_01'},
            {coords = vector3(-2081.5745, -1025.0431, 5.8815),  heading = 0.9659, model = 's_m_y_swat_01'},
            {coords = vector3(-2093.9202, -1021.6639, 5.8794),  heading = 253.8997, model = 's_m_y_swat_01'},
            {coords = vector3(-2097.7268, -1014.5998, 5.8843),  heading = 226.3138, model = 'ig_fbisuit_01'},
            {coords = vector3(-2041.3884, -1030.7418, 11.9807),  heading = 294.4532, model = 's_m_y_swat_01'},
            {coords = vector3(-2041.2856, -1032.1716, 11.9807),  heading = 229.4078, model = 's_m_y_swat_01'},
            {coords = vector3(-2042.5536, -1033.8352, 11.9807),  heading = 232.4358, model = 's_m_y_swat_01'},
            {coords = vector3(-2048.0989, -1025.8104, 11.9077),  heading = 298.0933, model = 's_m_y_swat_01'},
            {coords = vector3(-2047.5959, -1036.2426, 11.9076),  heading = 250.0438, model = 's_m_y_swat_01'},
            {coords = vector3(-2042.8683, -1025.3425, 11.7625),  heading = 233.4779, model = 's_m_y_swat_01'},
            {coords = vector3(-2037.0227, -1033.3210, 8.9715),  heading = 238.3270, model = 's_m_y_swat_01'},
            {coords = vector3(-2027.2030, -1036.9553, 6.7076),  heading = 72.8115, model = 's_m_y_swat_01'},
            {coords = vector3(-2029.1263, -1038.1953, 2.5663),  heading = 258.8150, model = 's_m_y_swat_01'},
            {coords = vector3(-2027.7499, -1034.8667, 2.5663),  heading = 232.9947, model = 's_m_y_swat_01'},
        },
        ['weapon'] = 'WEAPON_MACHINEPISTOL', -- You can change this
    },
    ['tables'] = { -- You can add new table with money/gold/weed/coke stacks.
        {coords = vector3(-2087.7, -1024.4, 4.88214), heading = 163.0, type = 'money'},
        {coords = vector3(-2099.6, -1020.3, 4.88214), heading = 163.0, type = 'weed'},
        {coords = vector3(-2093.0, -1008.2, 4.88214), heading = 342.0, type = 'gold'},
        {coords = vector3(-2081.8, -1012.1, 4.88214), heading = 342.0, type = 'money'},
        {coords = vector3(-2070.2, -1019.4, 4.88214), heading = 253.0, type = 'coke'},
    },
    ['artifacts'] = { -- You can add new artifacts.
        {coords = vector3(-2071.6, -1024.2, 5.76413),  heading = 250.0, model = 'vw_prop_vw_pogo_gold_01a',     itemName = 'pogo',     sellPrice = 100},
        {coords = vector3(-2085.4, -1013.9, 5.76413),  heading = 250.0, model = 'h4_prop_h4_t_bottle_02b',      itemName = 'bottle',   sellPrice = 100},
        {coords = vector3(-2089.7, -1009.4, 5.88413),  heading = 80.0,  model = 'h4_prop_h4_necklace_01a',      itemName = 'necklace', sellPrice = 100, displayObj = {model = 'h4_prop_h4_neck_disp_01a', rot = vector3(360.0, 0.0, 80.0)}},
        {coords = vector3(-2096.05, -1007.2, 5.88434), heading = 80.0,  model = 'h4_prop_h4_diamond_01a',       itemName = 'diamond',  sellPrice = 100, displayObj = {model = 'h4_prop_h4_diamond_disp_01a', rot = vector3(360.0, 0.0, 120.0)}},
        {coords = vector3(-2085.0, -1022.0, 5.80413),  heading = 0.0,   model = 'h4_prop_h4_art_pant_01a',      itemName = 'panther',  sellPrice = 100},
        {coords = vector3(-2099.6, -1014.7, 4.88413),  heading = 72.0,  model = 'vw_prop_casino_art_gun_02a',   itemName = 'artgun',   sellPrice = 100},
        {coords = vector3(-2078.0, -1022.35, 5.78434), heading = 75.0,  model = 'vw_prop_casino_art_skull_01b', itemName = 'artskull', sellPrice = 100},
        {coords = vector3(-2092.7, -1022.9, 5.56413),  heading = 152.0, model = 'vw_prop_casino_art_egg_01a',   itemName = 'artegg',   sellPrice = 100},
        {coords = vector3(-2096.1, -1022.0, 5.56413),  heading = 152.0, model = 'vw_prop_casino_art_lampf_01a', itemName = 'artlamp',  sellPrice = 100},
        {coords = vector3(-2078.4, -1025.75, 5.76413), heading = 192.0, model = 'vw_prop_casino_art_horse_01b', itemName = 'arthorse', sellPrice = 100},
    }
}

policeAlert = function(coords)
    if Config['YachtHeist']["dispatch"] == "default" then
        TriggerServerEvent('yachtheist:server:policeAlert', coords)
    elseif Config['YachtHeist']["dispatch"] == "cd_dispatch" then
        local data = exports['cd_dispatch']:GetPlayerInfo()
        TriggerServerEvent('cd_dispatch:AddNotification', {
            job_table = Config["YachtHeist"]['dispatchJobs'],
            coords = data.coords,
            title = '10-99 | YACHT HEIST',
            message = 'A '..data.sex..' is found robbing a yacht at '..data.street,
            flash = 1,
            unique_id = tostring(math.random(0000000,9999999)),
            blip = {
                sprite = 455,
                scale = 1.2,
                colour = 1,
                flashes = true,
                text = '911 - Yacht Heist',
                time = (8*60*1000),
                sound = 1,
            }
        })
    elseif Config['YachtHeist']["dispatch"] == "qs-dispatch" then
        exports['qs-dispatch']:YachtHeist()
    elseif Config['YachtHeist']["dispatch"] == "ps-dispatch" then
        exports['ps-dispatch']:YachtHeist()
    elseif Config['YachtHeist']["dispatch"] == "rcore_dispatch" then
        local data = {
            code = '10-64', -- string -> The alert code, can be for example '10-64' or a little bit longer sentence like '10-64 - Shop robbery'
            default_priority = 'high', -- 'low' | 'medium' | 'high' -> The alert priority
            coords = coords, -- vector3 -> The coords of the alert
            job = Config["YachtHeist"]['dispatchJobs'], -- string | table -> The job, for example 'police' or a table {'police', 'ambulance'}
            text = 'Yacht Robbery', -- string -> The alert text
            type = 'alerts', -- alerts | shop_robbery | car_robbery | bank_robbery -> The alert type to track stats
            blip_time = 5, -- number (optional) -> The time until the blip fades
            blip = { -- Blip table (optional)
                sprite = 431, -- number -> The blip sprite: Find them here (https://docs.fivem.net/docs/game-references/blips/#blips)
                colour = 3, -- number -> The blip colour: Find them here (https://docs.fivem.net/docs/game-references/blips/#blip-colors)
                scale = 1.2, -- number -> The blip scale
                text = 'Yacht Robbery', -- number (optional) -> The blip text
                flashes = false, -- boolean (optional) -> Make the blip flash
                radius = 0, -- number (optional) -> Create a radius blip instead of a normal one
            }
        }
        TriggerServerEvent('rcore_dispatch:server:sendAlert', data)
    end
end

Strings = {
    ['e_start'] = 'Press ~INPUT_CONTEXT~ to Start Yacht Heist',
    ['start_heist'] = 'Go to Yacht. Check your gps!',
    ['start_heist2'] = 'Required things for robbery: A lots of guns and bags',
    ['yacht_blip'] = 'Yacht',
    ['grab_stack'] = 'Press ~INPUT_CONTEXT~ to grab stack',
    ['grab_artifact'] = 'Press ~INPUT_CONTEXT~ to collect artifact',
    ['wait_nextrob'] = 'You have to wait this long to undress again',
    ['minute'] = 'minute.',
    ['need_this'] = 'You need this: ',
    ['need_police'] = 'Not enough police in the city.',
    ['total_money'] = 'You got this: ',
    ['police_alert'] = 'Yacht robbery alert! Check your gps.',
    ['not_cop'] = 'You are not cop!',
    ['buyer_blip'] = 'Buyer',
    ['deliver_to_buyer'] = 'Deliver the loot to the buyer. Check gps.',
    ['timerbar'] = 'COLLECTING'
}