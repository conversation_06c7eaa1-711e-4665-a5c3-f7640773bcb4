@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: transparent;
    overflow: hidden;
    color: #333;
}

.hidden {
    display: none !important;
}

#hms-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
    box-sizing: border-box;
    pointer-events: none;
}

#hms-container .hms-dashboard {
    pointer-events: all;
}

.hms-dashboard {
    display: flex;
    width: 1200px;
    height: 700px;
    background: rgba(0, 0, 0, 0.95);
    border-radius: 12px;
    border: 2px solid #333;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(207, 0, 67, 0.3);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: rgba(0, 0, 0, 0.8);
    color: #e0e0e0;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #333;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #333;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #cf0043, #a00034);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    box-shadow: 0 0 15px rgba(207, 0, 67, 0.4);
}

.logo-text .company {
    font-size: 16px;
    font-weight: 600;
    color: #cf0043;
}

.logo-text .subtitle {
    font-size: 12px;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #999;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: rgba(207, 0, 67, 0.1);
    color: #e0e0e0;
    border-left-color: rgba(207, 0, 67, 0.3);
}

.nav-item.active {
    background: rgba(207, 0, 67, 0.2);
    color: #cf0043;
    border-left-color: #cf0043;
    position: relative;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #cf0043;
    box-shadow: 0 0 10px rgba(207, 0, 67, 0.5);
}

.nav-icon {
    font-size: 18px;
    width: 20px;
    text-align: center;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid #333;
}

.close-btn {
    width: 100%;
    padding: 12px;
    background: transparent;
    border: 1px solid #444;
    color: #999;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: linear-gradient(135deg, #cf0043, #a00034);
    border-color: #cf0043;
    color: white;
    box-shadow: 0 0 15px rgba(207, 0, 67, 0.4);
}

/* Main Content */
.main-content {
    flex: 1;
    background: rgba(20, 20, 20, 0.9);
    display: flex;
    flex-direction: column;
}

.top-header {
    background: rgba(0, 0, 0, 0.7);
    padding: 15px 30px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title {
    font-size: 18px;
    font-weight: 600;
    color: #cf0043;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 8px 40px 8px 15px;
    border: 1px solid #444;
    border-radius: 6px;
    font-size: 14px;
    width: 250px;
    background: rgba(0, 0, 0, 0.5);
    color: #e0e0e0;
}

.search-box input::placeholder {
    color: #888;
}

.search-box input:focus {
    outline: none;
    border-color: #cf0043;
    box-shadow: 0 0 10px rgba(207, 0, 67, 0.3);
}

.search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

.police-count {
    font-size: 14px;
    color: #e0e0e0;
    font-weight: 500;
}

.refresh-btn {
    padding: 8px 12px;
    background: linear-gradient(135deg, #cf0043, #a00034);
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(207, 0, 67, 0.3);
}

.refresh-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #a00034, #800029);
    transform: translateY(-1px);
    box-shadow: 0 0 15px rgba(207, 0, 67, 0.5);
}

.refresh-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* Heist Table */
.heist-table-container {
    background: rgba(0, 0, 0, 0.6);
    border-radius: 8px;
    border: 1px solid #333;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.heist-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.heist-table thead {
    background: rgba(207, 0, 67, 0.1);
    border-bottom: 2px solid #cf0043;
}

.heist-table th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: #cf0043;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.heist-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #333;
    vertical-align: middle;
    color: #e0e0e0;
}

.heist-table tbody tr {
    transition: all 0.3s ease;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.3);
}

.heist-table tbody tr:hover {
    background: rgba(207, 0, 67, 0.1);
    transform: translateX(5px);
}

.heist-table tbody tr:last-child td {
    border-bottom: none;
}

/* Column widths */
.heist-name-col {
    width: 25%;
}

.status-col {
    width: 20%;
}

.police-col {
    width: 15%;
}

.cooldown-col {
    width: 15%;
}

.items-col {
    width: 15%;
}

.actions-col {
    width: 10%;
}

/* Table Status Elements */
.heist-name {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #e0e0e0;
}

.heist-icon {
    font-size: 18px;
    filter: drop-shadow(0 0 5px rgba(207, 0, 67, 0.3));
}

.status-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid transparent;
}

.status-badge.available {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border-color: #22c55e;
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
}

.status-badge.active {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border-color: #fbbf24;
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
}

.status-badge.cooldown {
    background: rgba(207, 0, 67, 0.2);
    color: #cf0043;
    border-color: #cf0043;
    box-shadow: 0 0 10px rgba(207, 0, 67, 0.3);
}

.status-badge.inactive {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border-color: #6c757d;
    box-shadow: 0 0 10px rgba(108, 117, 125, 0.3);
}

.police-info {
    color: #999;
    font-size: 13px;
    line-height: 1.3;
}

.police-info.sufficient {
    color: #28a745;
    font-weight: 500;
    text-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
}

.police-info.insufficient {
    color: #cf0043;
    font-weight: 500;
    text-shadow: 0 0 5px rgba(207, 0, 67, 0.3);
}

.police-required {
    font-weight: 600;
    margin-bottom: 2px;
}

.police-online {
    font-size: 11px;
    opacity: 0.8;
}

.cooldown-timer {
    color: #cf0043;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    text-shadow: 0 0 5px rgba(207, 0, 67, 0.3);
}

.required-items {
    color: #ccc;
    font-size: 12px;
}

.required-items.none {
    color: #28a745;
    font-style: italic;
    text-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
}

.action-btn {
    padding: 6px 12px;
    background: linear-gradient(135deg, #cf0043, #a00034);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 0 8px rgba(207, 0, 67, 0.3);
}

.action-btn:hover {
    background: linear-gradient(135deg, #a00034, #800029);
    transform: translateY(-1px);
    box-shadow: 0 0 12px rgba(207, 0, 67, 0.5);
}

.action-btn:disabled {
    background: #444;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Heist Details */
.heist-details {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    padding: 30px;
    border: 1px solid #333;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.7);
}

.details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #cf0043;
}

.details-title {
    font-size: 24px;
    font-weight: 700;
    color: #cf0043;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 10px rgba(207, 0, 67, 0.5);
}

.mark-location-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #cf0043, #a00034);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(207, 0, 67, 0.4);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.mark-location-btn:hover {
    background: linear-gradient(135deg, #a00034, #800029);
    transform: translateY(-2px);
    box-shadow: 0 0 20px rgba(207, 0, 67, 0.6);
}

.mark-location-btn:disabled {
    background: #444;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.detail-section {
    padding: 25px;
    background: rgba(20, 20, 20, 0.8);
    border-radius: 12px;
    border: 1px solid #444;
    border-left: 4px solid #cf0043;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.detail-section:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 25px rgba(207, 0, 67, 0.2);
    border-left-color: #ff1a5e;
}

.detail-section h4 {
    font-size: 18px;
    font-weight: 700;
    color: #cf0043;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 8px rgba(207, 0, 67, 0.3);
}

.detail-section ul, .detail-section ol {
    margin-left: 20px;
}

.detail-section li {
    margin-bottom: 12px;
    color: #e0e0e0;
    line-height: 1.6;
    font-size: 14px;
}

.detail-section strong {
    color: #cf0043;
    text-shadow: 0 0 5px rgba(207, 0, 67, 0.3);
}

/* Responsive design */
@media (max-width: 1024px) {
    .hms-dashboard {
        width: 95vw;
        height: 90vh;
        max-width: 1000px;
    }

    .content-area {
        padding: 20px;
    }

    .search-box input {
        width: 200px;
    }

    /* Hide less important columns on smaller screens */
    .items-col,
    .cooldown-col {
        display: none;
    }

    .heist-name-col {
        width: 35%;
    }

    .status-col {
        width: 25%;
    }

    .police-col {
        width: 30%;
    }

    .actions-col {
        width: 10%;
    }
}

@media (max-width: 768px) {
    .hms-dashboard {
        width: 98vw;
        height: 95vh;
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        flex-direction: row;
        overflow-x: auto;
    }

    .sidebar-header {
        min-width: 200px;
    }

    .sidebar-nav {
        display: flex;
        padding: 0;
        min-width: 600px;
    }

    .nav-item {
        min-width: 100px;
        justify-content: center;
        flex-direction: column;
        gap: 4px;
        padding: 10px;
    }

    .nav-text {
        font-size: 12px;
    }

    .sidebar-footer {
        min-width: 150px;
        display: flex;
        align-items: center;
    }

    .top-header {
        padding: 10px 15px;
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .header-right {
        justify-content: space-between;
    }

    .search-box input {
        width: 150px;
    }

    .content-area {
        padding: 15px;
    }

    .details-grid {
        grid-template-columns: 1fr;
    }

    /* Mobile table adjustments */
    .heist-table {
        font-size: 12px;
    }

    .heist-table th,
    .heist-table td {
        padding: 10px 8px;
    }

    /* Hide even more columns on mobile */
    .items-col,
    .cooldown-col,
    .actions-col {
        display: none;
    }

    .heist-name-col {
        width: 50%;
    }

    .status-col {
        width: 25%;
    }

    .police-col {
        width: 25%;
    }

    .heist-icon {
        font-size: 16px;
    }

    .status-badge {
        font-size: 10px;
        padding: 3px 8px;
    }
}

/* Scrollbar styling */
.content-area::-webkit-scrollbar {
    width: 6px;
}

.content-area::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 3px;
}

.content-area::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 3px;
}

.content-area::-webkit-scrollbar-thumb:hover {
    background: #a8b2ba;
}

/* Loading and error states */
.loading-state, .error-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading-state .icon, .error-state .icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.loading-state .message, .error-state .message {
    font-size: 16px;
    margin-bottom: 8px;
}

.loading-state .submessage, .error-state .submessage {
    font-size: 12px;
    color: #999;
}

/* Responsive design */
@media (max-width: 1024px) {
    .hms-dashboard {
        width: 95vw;
        height: 90vh;
        max-width: 800px;
    }

    .heists-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 15px;
    }

    .dashboard-content {
        padding: 20px;
    }
}

@media (max-width: 768px) {
    .hms-dashboard {
        width: 98vw;
        height: 95vh;
    }

    .dashboard-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .header-right {
        justify-content: space-between;
    }

    .heists-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .dashboard-content {
        padding: 15px;
    }
}
