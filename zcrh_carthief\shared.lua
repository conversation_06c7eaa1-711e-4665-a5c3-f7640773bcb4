Config = {}


Config.NPC = {
    model = 'g_m_y_famfor_01',
    locations = {
        vec4(765.9634, -3191.4658, 6.0383, 273.5765)
    }
}

Config.RequireItem = 'hacker_device'


Config.carModels = { -- Cars that can spawn
    'PLHUAYRA',
    'PLDIVO',
    'PLVALKYRIE',
}

Config.MinPolice = 3

Config.truckerDelay = 10 -- in seconds


Config.tuckerLocations = {
    {
        areaPosition = vec3(102.5413, -1937.4873, 20.8037), -- Grove Street
        vehPositions = {
            vec4(86.6116, -1970.4464, 20.7474, 325.3014),
            vec4(46.2755, -1915.8955, 21.6631, 317.0222),
        }
    },
    {
        areaPosition = vec3(1365.692261, -579.204407, 74.369995), -- Mirror Park
        vehPositions = {
            vec4(1359.969238, -609.375793, 74.336304, 0.000000),
            vec4(1355.920898, -547.411011, 73.780273, 155.905502),
        }
    },
    {
        areaPosition = vec3(-1079.6018, 444.3820, 74.8107), -- Mirror <PERSON>
        vehPositions = {
            vec4(-1076.3361, 465.4881, 77.3754, 150.6009),
            vec4(-1096.7062, 439.4742, 75.1462, 266.8044),
        }
    },
}

Config.destroyGPSTime = 120 --time after you can delete GPS from getting to car


Config.GPSRemove = 10000 -- Police blip time to delete after thief destroys GPS


Config.trackerHideoutLocations = { -- Locations to give back vehicle
    vec3(1977.7194, 5170.7856, 47.6391)
}

Config.lang = {
    ['talk_to_npc'] = "Talk",
    ['mission_in_progress'] = "I don't have anything for you now",
    ['car_location'] = "I marked the position on the GPS. The car to steal is an %s with the numbers %s",
    ['right_spot'] = "You're at the right spot, now find the car!",
    ['afk'] = "Trucker cancelled due to AFK",
    ['rid_of_gps'] = "Get rid of the GPS in the car, you will receive further instructions after doing this!",
    ['good_job'] = "Good job! Keep in touch!",
    ['stolen_vehicle'] = "Stolen Vehicle!",
    ['gps_off'] = "GPS off. I'm sending the locations to return the vehicle, just make sure no one is following you. Leave the vehicle there and get away",
    ['drop'] = "Drop site",
    ['taking_off_gps'] = "Takin off gps...",
    ['tow_the_vehicle'] = "Tow the vehicle",
    ['towing'] = "Towing the vehicle...",
    ['required_items'] = "You don't have required items!",
    ['gps_take_off'] = "Take off GPS"
}

Config.AFKProtect = 10 --- AFK time in minutes

Config.money = {
    type = "black_money", ---- 'black_money', 'bank', 'money'
    min = 18500, ---- min money
    max = 28750, ---- max money
}

-- Heist Notification Configuration
Config.HeistNotifications = {
    Success = {
        Use = true,
        missionTextLabel = "~b~CAR THEFT~s~",
        passFailTextLabel = "COMPLETED.",
        messageLabel = "Vehicle delivered successfully!"
    },
    Failed = {
        Use = true,
        missionTextLabel = "~b~CAR THEFT~s~",
        passFailTextLabel = "FAILED.",
        messageLabel = "The theft was unsuccessful."
    }
}