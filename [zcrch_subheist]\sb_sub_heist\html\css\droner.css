@keyframes slide-in-bottom {
  0% {
    transform: translateY(200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes slide-in-top {
  0% {
    transform: translateY(-200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes custom-animation {
  0% {
    opacity: 0.8;
    transform: scale(0.2);
  }
  80% {
    opacity: 0;
    transform: scale(1.2);
  }
  to {
    opacity: 0;
    transform: scale(2.2);
  }
}
@keyframes slide-in-left {
  0% {
    transform: translateX(-200px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.image-11-icon {
  position: absolute;
  top: 0;
  left: -981px;
  width: 3016px;
  height: 1692px;
  object-fit: cover;
  display: none;
}
.ellidronner {
  position: absolute;
  top: 428px;
  left: 1194px;
  border-radius: 50%;
  background-color: #aeaeae;
  filter: blur(250px);
  width: 841px;
  height: 841px;
}
.div {
  position: absolute;
  top: 0;
  left: 0;
  width: 1925px;
  height: 1083px;
  mix-blend-mode: screen;
}
.image-12-icon {
  position: absolute;
  top: 14px;
  left: 1px;
  width: 1920px;
  height: 1080px;
  object-fit: cover;
  display: none;
}
.dronnernoicw-icon {
  position: absolute;
  top: -476.9px;
  left: -961px;
  width: 2899.5px;
  height: 1631.8px;
  object-fit: cover;
}
.dronner11-icon,
.dronner22-icon,
.dronner33-icon {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 0;
}
.dronner33-icon {
  height: 77.13%;
  width: 100.41%;
  top: 24.17%;
  right: -0.3%;
  bottom: -1.3%;
  left: -0.1%;
  object-fit: contain;
}
.dronner33-icon.animate {
  animation: 1s ease 0s 1 normal forwards slide-in-bottom;
}
.dronner11-icon,
.dronner22-icon {
  height: 71.11%;
  width: 99.9%;
  top: 28.98%;
  right: 0.1%;
  bottom: -0.09%;
  left: 0;
}
.dronner22-icon.animate {
  animation: 1s ease 1s 1 normal forwards slide-in-bottom;
}
.dronner11-icon {
  height: 57.96%;
  width: 100.26%;
  top: 42.13%;
  right: -0.26%;
}
.dronner11-icon.animate {
  animation: 1s ease 2s 1 normal forwards slide-in-bottom;
}
.dronner3-icon,
.dronner4-icon,
.dronner5-icon,
.dronner8-icon,
.dronnerv2-icon {
  position: absolute;
  height: 5.11%;
  width: 4.34%;
  top: 71.04%;
  right: 41.9%;
  bottom: 23.85%;
  left: 53.76%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.dronner3-icon,
.dronner4-icon,
.dronner5-icon,
.dronner8-icon {
  height: 22.57%;
  width: 2.68%;
  top: 34.58%;
  right: 11.1%;
  bottom: 42.85%;
  left: 86.22%;
  opacity: 0;
}
.dronner8-icon.animate {
  animation: 1s ease 2s 1 normal forwards slide-in-top;
}
.dronner3-icon,
.dronner4-icon,
.dronner5-icon {
  height: 2.11%;
  width: 1.79%;
  top: 76.15%;
  right: 21.8%;
  bottom: 21.73%;
  left: 76.4%;
  opacity: 1;
}
.dronner5-icon.animate {
  animation: 1s ease 0s infinite normal forwards rotate-center;
}
.dronner3-icon,
.dronner4-icon {
  top: 45.88%;
  right: 89.73%;
  bottom: 52.01%;
  left: 8.48%;
}
.dronner2-icon.animate,
.dronner3-icon.animate,
.dronner4-icon.animate {
  animation: 2s ease 0s infinite normal forwards rotate-center;
}
.dronner3-icon {
  height: 4.04%;
  width: 3.43%;
  top: 24.19%;
  right: 78.86%;
  bottom: 71.77%;
  left: 17.7%;
}
.dronner2-icon {
  height: 4.38%;
  width: 3.72%;
  top: 44.81%;
  right: 16.24%;
  bottom: 50.81%;
  left: 80.04%;
  opacity: 1;
}
.dronner1-icon,
.dronner2-icon,
.dronnerselected-icon,
.dronnervector1-icon {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.dronner1-icon {
  height: 4.96%;
  width: 4.21%;
  top: 77.21%;
  right: 27.49%;
  bottom: 17.83%;
  left: 68.3%;
  opacity: 0;
}
.dronner1-icon.animate {
  animation: 1s ease 0s infinite normal forwards custom-animation;
}
.dronnerselected-icon,
.dronnervector1-icon {
  height: 2.8%;
  width: 2.38%;
  top: 80.45%;
  right: 64.27%;
  bottom: 16.74%;
  left: 33.35%;
}
.dronnerselected-icon {
  height: 2.95%;
  width: 8.92%;
  top: 76.84%;
  right: 86.53%;
  bottom: 20.21%;
  left: 4.55%;
}
.hecdond-icon {
  height: 16.84%;
  width: 0.91%;
  top: 9.4%;
  right: 93.1%;
  bottom: 73.76%;
  left: 5.99%;
  max-width: 100%;
  max-height: 100%;
  opacity: 1;
}
.hecdond-icon.animate {
  animation: 1s ease-in-out 3s infinite alternate-reverse none slide-in-top;
}
.framedronner,
.hecdond-icon,
.vecdord-icon {
  position: absolute;
  overflow: hidden;
}
.vecdord-icon {
  height: 1.12%;
  width: 14.9%;
  top: 81.34%;
  right: 80.57%;
  bottom: 17.54%;
  left: 4.53%;
  max-width: 100%;
  max-height: 100%;
  opacity: 1;
}
.vecdord-icon.animate {
  animation: 1s ease-in-out 3s infinite alternate-reverse both slide-in-left;
}
.framedronner {
  top: -33px;
  left: -44px;
  width: 1568px;
  height: 1330.2px;
}
.dronnervector-icon {
  position: absolute;
  top: -111px;
  left: 1365px;
  width: 497.8px;
  height: 1464.6px;
  opacity: 0;
}
.dronnervector-icon.animate {
  animation: 9s ease 3s 1 normal forwards slide-in-top;
}
.dronner {
  width: 100%;
  position: relative;
  height: 1080px;
  overflow: hidden;
}
