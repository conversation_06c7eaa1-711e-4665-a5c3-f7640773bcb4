<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_575_1556)">
<path d="M9.37109 6.83192L4.8557 4.57422V9.08961L9.37109 6.83192Z" fill="url(#paint0_linear_575_1556)"/>
</g>
<defs>
<filter id="filter0_d_575_1556" x="0.841785" y="0.560535" width="12.543" height="12.543" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.00684"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_575_1556"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_575_1556" result="shape"/>
</filter>
<linearGradient id="paint0_linear_575_1556" x1="4.8557" y1="9.08961" x2="9.8728" y2="9.08961" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#9B9B9B"/>
</linearGradient>
</defs>
</svg>
