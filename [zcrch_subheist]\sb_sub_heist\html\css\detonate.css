@keyframes pulsate {
  0% {
    transform: scale(1);
    transform-origin: center center;
    animation-timing-function: ease-out;
  }
  10% {
    transform: scale(0.91);
    animation-timing-function: ease-in;
  }
  17% {
    transform: scale(0.98);
    animation-timing-function: ease-out;
  }
  33% {
    transform: scale(0.87);
    animation-timing-function: ease-in;
  }
  45% {
    transform: scale(1);
    animation-timing-function: ease-out;
  }
}
@keyframes custom-animation {
  0% {
    opacity: 0.8;
    transform: scale(0.2);
  }
  80% {
    opacity: 0;
    transform: scale(1.2);
  }
  to {
    opacity: 0;
    transform: scale(2.2);
  }
}
@keyframes slide-in-bottom {
  0% {
    transform: translateY(200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
.detonate-child {
  width: 1565px;
  position: relative;
  height: 993px;
  overflow: hidden;
  flex-shrink: 0;
}
.detonate-icon1 {
  position: absolute;
  top: 275.5px;
  left: calc(50% - 78.2px);
  width: 156.5px;
  height: 28.7px;
}
.paste-image-here1 {
  position: absolute;
  top: 8.8px;
  left: 6.4px;
  border-radius: 14.71px;
  background-color: var(--color-gray-100);
  width: 252.1px;
  height: 252.9px;
}
.f1ea8b4632ecf76d693afd835bcd26-icon {
  position: absolute;
  top: 3.2px;
  left: 2.7px;
  width: 165.7px;
  height: 165.7px;
  object-fit: cover;
  display: none;
  opacity: 0.4;
}
.f1ea8b4632ecf76d693afd835bcd26 {
  position: absolute;
  top: 0;
  left: 2.7px;
  background: linear-gradient(180deg, #fff, rgba(255, 255, 255, 0));
  width: 165.7px;
  height: 165.7px;
  opacity: 0.4;
  mix-blend-mode: screen;
}
.frame-child9 {
  position: absolute;
  top: 33.1px;
  left: 23.6px;
  width: 122.4px;
  height: 105.6px;
}
.frame-child10,
.vector-icon1 {
  position: absolute;
  top: 32.1px;
  left: 22.6px;
  width: 124.4px;
  height: 107.9px;
}
.frame-child10 {
  top: 96.3px;
  left: 84px;
  width: 32px;
  height: 32px;
}
.f1ea8b4632ecf76d693afd835bcd26-parent {
  position: absolute;
  top: 9.2px;
  left: 9.2px;
  border-radius: 90.31px;
  background-color: rgba(255, 255, 255, 0.01);
  box-shadow: 0 0.5px 6.02px rgba(255, 255, 255, 0.6) inset,
    0 0 5.02px rgba(255, 255, 255, 0.3) inset;
  width: 171.6px;
  height: 171.6px;
  overflow: hidden;
}
.frame-child11,
.frame-child12 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  border: 0.8px solid var(--color-darkgray);
  box-sizing: border-box;
  width: 190.6px;
  height: 190.6px;
}
.frame-child12 {
  top: -33.5px;
  left: 112.5px;
  width: 152.4px;
  height: 202.8px;
  transform: rotate(44.82deg);
  transform-origin: 0 0;
}
.mask-group-icon1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 190.6px;
  height: 190.6px;
  object-fit: cover;
}
.frame-child13,
.frame-child14,
.frame-child15,
.frame-child16 {
  position: absolute;
  top: 54.1px;
  left: 180.4px;
  border-radius: 50%;
  background-color: var(--color-white);
  filter: blur(11.54px);
  width: 4.4px;
  height: 14.6px;
  transform: rotate(-12.9deg);
  transform-origin: 0 0;
}
.frame-child14,
.frame-child15,
.frame-child16 {
  top: 4px;
  left: 108.4px;
  width: 2px;
  height: 14.5px;
  transform: rotate(-90deg);
}
.frame-child15,
.frame-child16 {
  top: 178.7px;
  left: 42.3px;
  width: 3.1px;
  height: 14.6px;
  transform: rotate(-74.67deg);
}
.frame-child16 {
  top: 124.2px;
  left: 3.5px;
  transform: rotate(-15deg);
}
.frame-child17 {
  position: absolute;
  top: 92.8px;
  left: 188.1px;
  width: 4.5px;
  height: 4.5px;
}
.frame-child18,
.frame-child19,
.frame-child20 {
  position: absolute;
  top: 187.9px;
  left: 93.1px;
  width: 4.5px;
  height: 4.5px;
  object-fit: contain;
}
.frame-child19,
.frame-child20 {
  top: 92.8px;
  left: -2px;
}
.frame-child20 {
  top: -2.3px;
  left: 93.1px;
}
.clip-path-group1,
.group-icon3,
.group-icon4 {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.clip-path-group1 {
  height: 90.4%;
  width: 90.4%;
  top: 4.67%;
  right: 4.98%;
  bottom: 4.93%;
  left: 4.62%;
  object-fit: cover;
}
.group-icon3,
.group-icon4 {
  height: 62.2%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 37.8%;
  left: 0;
}
.group-icon4 {
  height: 22.05%;
  width: 48.71%;
  top: 77.95%;
  right: 5.9%;
  bottom: 0;
  left: 45.39%;
}
.group-icon5,
.group2 {
  position: absolute;
  height: 100%;
  width: 38.71%;
  top: 0;
  right: 61.29%;
  bottom: 0;
  left: 0;
}
.group-icon5 {
  height: 25.98%;
  width: 56.71%;
  right: 0;
  bottom: 74.02%;
  left: 43.29%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.group-group {
  position: absolute;
  height: 19.99%;
  width: 36.73%;
  top: -12.54%;
  right: 65.37%;
  bottom: 92.55%;
  left: -2.1%;
}
.frame-container {
  position: absolute;
  top: 51.9px;
  left: calc(50% - 95.7px);
  border-radius: 100.34px;
  width: 190.6px;
  height: 190.6px;
}
.group-48096126-1 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 56px;
  width: 135px;
  height: 132px;
  object-fit: cover;
}
.vector-icon2 {
  position: absolute;
  top: calc(50% - 54px);
  left: calc(50% - 54.4px);
  width: 108.3px;
  height: 108.4px;
  object-fit: contain;
}
.group-48096126-1-parent {
  cursor: pointer;
  border: 0;
  padding: 0;
  background-color: transparent;
  position: absolute;
  top: 312px;
  left: calc(50% - 67.4px);
  width: 135px;
  height: 132px;
}
.group-48096126-1-parent:hover {
  animation: 5s ease-in-out 0s infinite normal none pulsate;
  opacity: 1;
}
.stylecard21 {
  width: 264.8px;
  position: relative;
  border-radius: 19.62px;
  background-color: var(--color-gray-200);
  box-shadow: -4.8px 11.2px 19.94px rgba(0, 0, 0, 0.5);
  border: 1.6px solid var(--color-white);
  box-sizing: border-box;
  height: 453.1px;
  overflow: hidden;
  flex-shrink: 0;
  z-index: 0;
}
.frame-child21,
.vector-icon3 {
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.frame-child21 {
  width: 88.52%;
  position: absolute;
  margin: 0 !important;
  height: 51.05%;
  top: 4.04%;
  right: 5.74%;
  bottom: 44.91%;
  left: 5.74%;
  z-index: 1;
}
.vector-icon3 {
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
}
.vector-icon3.animate {
  animation: 1.5s ease 0s infinite normal forwards custom-animation;
}
.frame1,
.group3,
.vector-icon3 {
  position: absolute;
}
.group3 {
  height: 102.97%;
  width: 101.73%;
  top: 3.03%;
  right: 0;
  bottom: -6%;
  left: -1.73%;
}
.frame1 {
  width: 185.1px;
  margin: 0 !important;
  top: 55px;
  left: 33px;
  height: 185.1px;
  overflow: hidden;
  flex-shrink: 0;
  mix-blend-mode: color-dodge;
  z-index: 2;
}
.detonate,
.stylecard2-group {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-end;
  gap: 0 10px;
}
.stylecard2-group {
  opacity: 0;
}
.stylecard2-group.animate {
  animation: 1s ease 0s 1 normal forwards slide-in-bottom;
}
.detonate {
  width: 100%;
  height: 1080px;
  overflow: hidden;
  padding: 87px 80px;
  box-sizing: border-box;
}
