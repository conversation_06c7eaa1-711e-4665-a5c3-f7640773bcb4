Config = {}

Config.MinWithdrawl = 40000 -- $ received minimum for successful hack
Config.MaxWithdrawal = 100000 -- $ received maximum for successful hack
Config.Account = "black_money" -- money | black_money | bank
Config.MingameBase = 10 -- 10 for decimal or 16 for hexadecimal
Config.MinigameTimeLimit = 90 -- time limit in seconds
Config.MingameAttemptLimit = 8 -- number of guesses allowed

Config.ATMModels = {
    `prop_atm_01`, -- 20 on map
    `prop_atm_02`, -- 37 on map
    `prop_atm_03`, -- 30 on map
    `prop_fleeca_atm` -- 4 on map
}

Config.ATMModelsString = {
    "prop_atm_01", -- 20 on map
    "prop_atm_02", -- 37 on map
    "prop_atm_03", -- 30 on map
    "prop_fleeca_atm" -- 4 on map
}

Config.Chance = 0.25 -- Chance of police alert on successful hack. Note: police are always alerted on failed hack.

-- Required item for ATM hacking
Config.RequiredItem = "hack_usb"

-- Police requirements
Config.RequiredCopsCount = 1 -- Minimum police officers required to start ATM robbery
Config.CopsJobs = {'police'} -- Police job names

-- Heist Notification Configuration
Config.HeistNotifications = {
    Success = {
        Use = true,
        missionTextLabel = "~b~ATM ROBBERY~s~",
        passFailTextLabel = "COMPLETED.",
        messageLabel = "Cash extracted! Escape before police arrive!"
    },
    Failed = {
        Use = true,
        missionTextLabel = "~b~ATM ROBBERY~s~",
        passFailTextLabel = "FAILED.",
        messageLabel = "Hack unsuccessful. Run away!"
    }
}

