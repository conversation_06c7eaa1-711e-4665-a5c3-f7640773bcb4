local ESX = exports['es_extended']:getSharedObject()

local currentNPCPosition = Config.NPC.locations[math.random(1, #Config.NPC.locations)]
local tracker = {
    state = false,
    lastTruckerEnded = 0,
    thief = nil,
    car = nil,
    pos = {
        area = nil,
        carPosition = nil
    }
}

local ClearTrackerState = function()
    tracker = {
        state = false,
        lastTruckerEnded = os.time(),
        thief = nil,
        car = nil,
        pos = {
            area = nil,
            carPosition = nil
        }
    }
end

RegisterNetEvent('zcrh_carthief:server:endTrucker', function()
    local src = source
    ClearTrackerState()
    local xPlayer = ESX.GetPlayerFromId(src)
    local payment = math.random(Config.money.min, Config.money.max)
    xPlayer.addAccountMoney(Config.money.type, payment)

    -- Show heist success with actual payment
    if Config.HeistNotifications.Success.Use then
        local rewards = {
            {
                stat = "Payment",
                value = "~g~$" .. payment
            }
        }
        TriggerClientEvent('heist-notify:show', src, Config.HeistNotifications.Success, rewards, 7, true)
    end

    -- Notify HMS that car theft ended
    TriggerEvent('hms:heistEnded', 'carthief')
    TriggerEvent('hms:updateCooldown', 'carthief', os.time(), 10) -- 10 second cooldown
end)

ESX.RegisterServerCallback('zcrh_carthief:callback:GetNPCPosition', function(src, cb)
    cb(currentNPCPosition)
end)

ESX.RegisterServerCallback('zcrh_carthief:callback:canStartTracker', function(src, cb)
    print(#ESX.GetExtendedPlayers('job', 'police'))
    cb(os.time() - tracker.lastTruckerEnded >= Config.truckerDelay and not tracker.state and #ESX.GetExtendedPlayers('job', 'police') >= Config.MinPolice)
end)

RegisterNetEvent('zcrh_carthief:server:startTucker', function()
    local src = source
    if tracker.state then
        return
    end
    tracker.state = true
    tracker.thief = src
    -- Notify HMS that car theft started
    TriggerEvent('hms:heistStarted', 'carthief')
    local truckerPosInfo = Config.tuckerLocations[math.random(1, #Config.tuckerLocations)]
    tracker.pos.area = truckerPosInfo.areaPosition
    tracker.pos.carPosition = truckerPosInfo.vehPositions[math.random(1, #truckerPosInfo.vehPositions)]
    TriggerClientEvent('zcrh_carthief:client:startTucker', src, {
        area = tracker.pos.area,
        carPosition = tracker.pos.carPosition
    })
end)

RegisterNetEvent('zcrh_carthief:server:setTruckerCar', function(netId)
    tracker.car = netId
end)

RegisterNetEvent('zcrh_carthief:server:policeGPS', function(coords)
    local xPlayers = ESX.GetExtendedPlayers('job', 'police')
    for _, xPlayer in pairs(xPlayers) do
            TriggerClientEvent('zcrh_carthief:client:policeGPS', xPlayer.source, coords, tracker.car)
    end
end)

RegisterNetEvent('zcrh_carthief:server:truckerDestroy', function()
    TriggerClientEvent('zcrh_carthief:client:truckerDestroy', tracker.thief)
    ClearTrackerState()
    -- Notify HMS that car theft failed/cancelled
    TriggerEvent('hms:heistFailed', 'carthief')
    TriggerEvent('hms:updateCooldown', 'carthief', os.time(), 10) -- 10 second cooldown
    local xPlayers = ESX.GetExtendedPlayers('job', 'police')
    for _, xPlayer in pairs(xPlayers) do
            TriggerClientEvent('zcrh_carthief:client:GPSRemoveForced', xPlayer.source)
    end
end)

RegisterNetEvent('zcrh_carthief:server:GPSRemoved', function()
    local xPlayers = ESX.GetExtendedPlayers('job', 'police')
    for _, xPlayer in pairs(xPlayers) do
            TriggerClientEvent('zcrh_carthief:client:GPSRemoved', xPlayer.source)
    end
end)

AddEventHandler('playerDropped', function (reason)
    local src = source
    if src == tracker.thief then
        ClearTrackerState()
        local xPlayers = ESX.GetExtendedPlayers('job', 'police')
        for _, xPlayer in pairs(xPlayers) do
                TriggerClientEvent('zcrh_carthief:client:GPSRemoveForced', xPlayer.source)
        end
    end
  end)

  RegisterNetEvent('zcrh_carthief:server:removeitem', function(item)
    local src = source
    exports.ox_inventory:RemoveItem(src, item, 1)
  end)