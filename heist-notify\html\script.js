// Heist Management System - Frontend JavaScript

let heistData = {};
let currentPage = 'overview';
let policeCount = 0;

// Heist configuration - will be loaded dynamically from server
let heistConfig = {};


// Listen for messages from the game
window.addEventListener('message', function(event) {
    const data = event.data;

    switch(data.type) {
        case 'openHMS':
            openHMS();
            break;
        case 'closeHMS':
            closeHMS();
            break;
        case 'updateStatus':
            updateAllHeistStatus(data.data);
            break;
        case 'updateSingleStatus':
            updateSingleHeistStatus(data.heistType, data.data);
            break;
    }
});

// Open HMS interface
function openHMS() {
    document.getElementById('hms-container').classList.remove('hidden');
    setupNavigation();
    showLoadingState();
    // Request initial data
    fetch(`https://${GetParentResourceName()}/requestStatus`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    });
}

// Close HMS interface
function closeHMS() {
    document.getElementById('hms-container').classList.add('hidden');
    // Send close event to game to handle cursor
    fetch(`https://${GetParentResourceName()}/closeHMS`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    });
}

// Show loading state
function showLoadingState() {
    const grid = document.getElementById('dashboard-grid');
    grid.innerHTML = `
        <div class="loading-state">
            <div class="icon">🔄</div>
            <div class="message">Loading heist data...</div>
        </div>
    `;
}

// Setup navigation functionality
function setupNavigation() {
    // Navigation switching
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function() {
            const targetPage = this.getAttribute('data-page');
            switchPage(targetPage);
        });
    });

    // Close button functionality
    document.getElementById('close-btn').addEventListener('click', closeHMS);

    // Refresh button functionality
    document.getElementById('refresh-btn').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.textContent;
        btn.textContent = '⏳';
        btn.disabled = true;

        fetch(`https://${GetParentResourceName()}/refreshStatus`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        }).finally(() => {
            setTimeout(() => {
                btn.textContent = originalText;
                btn.disabled = false;
            }, 1000);
        });
    });

    // ESC key to close
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeHMS();
        }
    });

    // Search functionality
    document.getElementById('search-input').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        filterHeists(searchTerm);
    });
}

// Switch between pages
function switchPage(pageId) {
    currentPage = pageId;

    // Update active nav item
    document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
    document.querySelector(`[data-page="${pageId}"]`).classList.add('active');

    // Update active page
    document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));

    let targetPageElement = document.getElementById(`page-${pageId}`);
    if (!targetPageElement && pageId !== 'overview') {
        // Create individual heist page if it doesn't exist
        createHeistPage(pageId);
        targetPageElement = document.getElementById(`page-${pageId}`);
    }

    if (targetPageElement) {
        targetPageElement.classList.add('active');
    }

    // Update page title
    const navText = document.querySelector(`[data-page="${pageId}"] .nav-text`).textContent;
    document.querySelector('.page-title').textContent = navText === 'Dashboard' ? 'Dashboard' : navText;
}

// Create individual heist page
function createHeistPage(heistId) {
    const config = heistConfig[heistId];
    const data = heistData[heistId];

    if (!config && !data) {
        console.warn('No configuration or data found for heist:', heistId);
        return;
    }

    const contentArea = document.querySelector('.content-area');
    const pageDiv = document.createElement('div');
    pageDiv.id = `page-${heistId}`;
    pageDiv.className = 'page';

    const hasLocation = config && config.coords !== null;
    const name = config ? config.name : (data ? data.name : heistId);
    const requiredCops = config ? config.requiredCops : (data ? data.requiredCops : 'Unknown');
    const items = config ? (config.items || 'None') : 'Unknown';
    const cooldown = config ? config.cooldown : 'Unknown';
    const location = config ? config.location : (data ? data.location : 'Unknown');
    const instructions = config ? config.instructions : ['No instructions available'];
    const reward = config ? config.reward : 'Unknown';
    const experience = config ? config.experience : 'Unknown';
    const riskLevel = config ? config.riskLevel : 'Unknown';
    const rewardType = config ? config.rewardType : 'cash';

    pageDiv.innerHTML = `
        <div class="heist-details">
            <div class="details-header">
                <div class="details-title">${name} Details</div>
                ${hasLocation ? `<button class="mark-location-btn" onclick="markLocation('${heistId}')">📍 Mark Location</button>` : ''}
            </div>
            <div class="details-grid">
                <div class="detail-section">
                    <h4>Requirements</h4>
                    <ul>
                        <li>Police Required: <strong>${requiredCops} officer${requiredCops > 1 ? 's' : ''}</strong></li>
                        <li>Items Needed: <strong>${items}</strong></li>
                        <li>Cooldown: <strong>${cooldown}</strong></li>
                        <li>Location: <strong>${location}</strong></li>
                    </ul>
                </div>
                <div class="detail-section">
                    <h4>Instructions</h4>
                    <ol>
                        ${instructions.map(instruction => `<li>${instruction}</li>`).join('')}
                    </ol>
                </div>
                <div class="detail-section">
                    <h4>Rewards</h4>
                    <ul>
                        ${rewardType === 'items' ?
                            `<li>Items: <strong>${reward}</strong></li>` +
                            (config && config.rewardItems ?
                                config.rewardItems.map(item =>
                                    `<li>${item.name}: <strong>${item.count} pieces (${item.value})</strong></li>`
                                ).join('') : ''
                            ) :
                            `<li>Cash: <strong>${reward}</strong></li>`
                        }
                        <li>Experience: <strong>${experience}</strong></li>
                        <li>Risk Level: <strong>${riskLevel}</strong></li>
                    </ul>
                </div>
            </div>
        </div>
    `;

    contentArea.appendChild(pageDiv);
}

// Mark location function
function markLocation(heistId) {
    const config = heistConfig[heistId];
    if (config && config.coords) {
        fetch(`https://${GetParentResourceName()}/markLocation`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                heistId: heistId,
                coords: config.coords
            })
        });
    }
}

// Filter heists based on search term
function filterHeists(searchTerm) {
    const cards = document.querySelectorAll('.stat-card');
    cards.forEach(card => {
        const title = card.querySelector('.stat-title').textContent.toLowerCase();
        if (title.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Update all heist statuses
function updateAllHeistStatus(data) {
    // Handle both old and new data formats
    if (data.heists) {
        heistData = data.heists;
        policeCount = data.policeCount || 0;

        // Load heist configurations from the received data
        if (data.configs) {
            heistConfig = data.configs;
        }
    } else {
        heistData = data;
        policeCount = 0; // Default if no police count provided
    }
    updatePoliceCount(policeCount);
    renderStatCards();
}

// Update police count display
function updatePoliceCount(count) {
    document.getElementById('police-count').textContent = count;
}

// Update single heist status
function updateSingleHeistStatus(heistType, data) {
    heistData[heistType] = data;
    renderStatCards();
}

// Render heist table for dashboard
function renderStatCards() {
    const tableBody = document.getElementById('heist-table-body');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    if (Object.keys(heistData).length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                    <div>❌ No heist data available</div>
                    <div style="font-size: 12px; margin-top: 8px;">Click refresh to try again</div>
                </td>
            </tr>
        `;
        return;
    }

    for (const [heistType, data] of Object.entries(heistData)) {
        const row = createHeistRow(heistType, data);
        tableBody.appendChild(row);
    }
}

// Create individual heist table row
function createHeistRow(heistType, data) {
    const config = heistConfig[heistType];
    if (!config) return document.createElement('tr');

    const row = document.createElement('tr');
    row.onclick = () => switchPage(heistType);

    const statusText = getStatusText(data.status);
    const statusClass = getStatusClass(data.status);

    // Determine status display
    let statusDisplay = statusText;
    let policeDisplay = '';
    let cooldownDisplay = '-';
    let policeClass = '';

    switch(data.status) {
        case 'ready':
            policeDisplay = `<div class="police-required">${data.requiredCops} required</div><div class="police-online">${policeCount} online</div>`;
            policeClass = 'sufficient';
            break;
        case 'ongoing':
            policeDisplay = `<div class="police-required">${data.requiredCops} required</div><div class="police-online">${policeCount} online</div>`;
            policeClass = 'sufficient';
            break;
        case 'cooldown':
            cooldownDisplay = formatTime(data.cooldownRemaining || 0);
            policeDisplay = `<div class="police-required">${data.requiredCops} required</div><div class="police-online">${policeCount} online</div>`;
            policeClass = policeCount >= data.requiredCops ? 'sufficient' : 'insufficient';
            break;
        case 'no_police':
            policeDisplay = `<div class="police-required">${data.requiredCops} required</div><div class="police-online">${policeCount} online</div>`;
            policeClass = 'insufficient';
            break;
        default:
            policeDisplay = `<div class="police-required">${data.requiredCops} required</div><div class="police-online">${policeCount} online</div>`;
            policeClass = '';
    }

    // Required items display
    const itemsDisplay = config.items && config.items !== 'None' ? config.items : 'None';
    const itemsClass = itemsDisplay === 'None' ? 'none' : '';

    // Location button
    const hasLocation = config && config.coords !== null;
    const locationButton = hasLocation ?
        `<button class="action-btn" onclick="event.stopPropagation(); markLocation('${heistType}')">📍</button>` :
        '-';

    row.innerHTML = `
        <td>
            <div class="heist-name">
                <span class="heist-icon">${config.icon || '🎯'}</span>
                <span>${config.name}</span>
            </div>
        </td>
        <td>
            <span class="status-badge ${statusClass}">${statusDisplay}</span>
        </td>
        <td>
            <span class="police-info ${policeClass}">${policeDisplay}</span>
        </td>
        <td>
            ${data.status === 'cooldown' ? `<span class="cooldown-timer">${cooldownDisplay}</span>` : cooldownDisplay}
        </td>
        <td>
            <span class="required-items ${itemsClass}">${itemsDisplay}</span>
        </td>
        <td>
            ${locationButton}
        </td>
    `;

    return row;
}

// Get status text
function getStatusText(status) {
    switch(status) {
        case 'ready':
            return 'AVAILABLE';
        case 'ongoing':
            return 'ACTIVE';
        case 'cooldown':
            return 'COOLDOWN';
        case 'no_police':
            return 'INACTIVE';
        default:
            return 'UNKNOWN';
    }
}

// Get status CSS class
function getStatusClass(status) {
    switch(status) {
        case 'ready':
            return 'available';
        case 'ongoing':
            return 'active';
        case 'cooldown':
            return 'cooldown';
        case 'no_police':
            return 'inactive';
        default:
            return 'unknown';
    }
}

// Format time in MM:SS format
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Update cooldown timers every second
setInterval(() => {
    let needsUpdate = false;
    for (const [heistType, data] of Object.entries(heistData)) {
        if (data.status === 'cooldown' && data.cooldownRemaining > 0) {
            data.cooldownRemaining = Math.max(0, data.cooldownRemaining - 1);
            needsUpdate = true;

            if (data.cooldownRemaining <= 0) {
                data.status = 'ready';
            }
        }
    }
    if (needsUpdate && currentPage === 'overview') {
        renderStatCards();
    }
}, 1000);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setupNavigation();
});

// Helper function to get resource name
function GetParentResourceName() {
    return 'heist-notify';
}
