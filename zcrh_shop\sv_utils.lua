local YourWebhook = 'https://discord.com/api/webhooks/1138767859766534184/BGEMEaR_ORAFqlJT42Wj2i-EeKFohqooC_3nfdHdCC9TGrF7L3MV1iqf1C12JI513Epb'  -- help: https://docs.brutalscripts.com/site/others/discord-webhook

function GetWebhook()
    return YourWebhook
end

function notification(source, title, text, time, type)
    if Config.BrutalNotify then
        TriggerClientEvent('brutal_notify:SendAlert', source, title, text, time, type)
    else
        TriggerClientEvent('zcrh_shop:client:DefaultNotify', source, text)
    end
end

function PlayerNameFunction(source)
    return GetPlayerName(source)
end

function SendWebhookLog(type, message)
    local webhookUrl = GetWebhook()
    local color = Config.Webhooks.Colors[type] or 16777215 -- Default to white color
    local embed = {
        {
            ["color"] = color,
            ["title"] = Config.Webhooks.Locale[type] or "Webhook Log",
            ["description"] = message,
            ["footer"] = {
                ["text"] = os.date("%Y-%m-%d %H:%M:%S"),
            },
        }
    }
    PerformHttpRequest(webhookUrl, function(err, text, headers) end, "POST", json.encode({embeds = embed}), {["Content-Type"] = "application/json"})
end