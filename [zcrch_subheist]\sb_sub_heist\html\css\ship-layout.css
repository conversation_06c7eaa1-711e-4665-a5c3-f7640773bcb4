@keyframes jello-horizontal {
  0%,
  to {
    transform: scale3d(1, 1, 1);
  }
  30% {
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    transform: scale3d(1.05, 0.95, 1);
  }
}
@keyframes custom-animation {
  0% {
    opacity: 0.8;
    transform: scale(0.2);
  }
  80% {
    opacity: 0;
    transform: scale(1.2);
  }
  to {
    opacity: 0;
    transform: scale(2.2);
  }
}
@keyframes pulsate {
  0% {
    transform: scale(1);
    transform-origin: center center;
    animation-timing-function: ease-out;
  }
  10% {
    transform: scale(0.91);
    animation-timing-function: ease-in;
  }
  17% {
    transform: scale(0.98);
    animation-timing-function: ease-out;
  }
  33% {
    transform: scale(0.87);
    animation-timing-function: ease-in;
  }
  45% {
    transform: scale(1);
    animation-timing-function: ease-out;
  }
}
.shiplayoutstylerectangle-1254 {
  position: absolute;
  top: calc(50% - 461.5px);
  left: calc(50% - 866px);
  border-radius: var(--br-6xl);
  border: 1px solid #156017;
  box-sizing: border-box;
  width: 1731px;
  height: 923px;
}
.shiplayoutgridvector-icon {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 0.15;
}
.shiplayoutbutton-icon,
.shiplayoutbutton-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 178px;
  height: 76px;
}
.shiplayoutbutton-icon:hover {
  animation: 1s linear 0s infinite normal none jello-horizontal;
  opacity: 1;
}
.shiplayoutbutton-wrapper {
  cursor: pointer;
  border: 0;
  padding: 0;
  background-color: transparent;
  top: 797px;
  left: 1485px;
}
.weakstrongvector-icon {
  position: absolute;
  top: 846.9px;
  left: 45px;
  width: 225.4px;
  height: 29.2px;
}
.shiplayoutvector-745-icon {
  position: absolute;
  top: 465px;
  left: 300.4px;
  width: 1270px;
  height: 264px;
}
.shiplayoutvector2-icon {
  position: absolute;
  height: 11.49%;
  width: 90.32%;
  top: 67.9%;
  right: 4.77%;
  bottom: 20.61%;
  left: 4.91%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.shiplayoutstyletextvector-icon,
.shiplayoutvector-icon,
.shiplayoutvector-icon1 {
  position: absolute;
  height: 6.29%;
  width: 66.26%;
  top: 84.64%;
  right: 16.75%;
  bottom: 9.07%;
  left: 16.98%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.shiplayoutvector-icon,
.shiplayoutvector-icon1 {
  height: 7.43%;
  width: 20.38%;
  top: 51.63%;
  right: 45.48%;
  bottom: 40.94%;
  left: 34.14%;
}
.shiplayoutvector-icon1 {
  height: 57.21%;
  width: 94.71%;
  top: 22.22%;
  right: 2.69%;
  bottom: 20.57%;
  left: 2.6%;
}
.s4 {
  top: 71.9%;
  left: 12.54%;
  opacity: 0;
}
.s2.animate,
.s4.animate {
  animation: 1s ease 0s infinite normal forwards custom-animation;
}
.s2,
.s3,
.s4,
.s41 {
  position: absolute;
  text-transform: uppercase;
  text-shadow: 0 0 8.42px rgba(255, 255, 255, 0.5);
}
.s2 {
  top: 51.63%;
  left: 9.53%;
  opacity: 0;
}
.s3,
.s41 {
  top: 71.9%;
  left: 85.15%;
}
.s3 {
  top: 68.08%;
  left: 60.49%;
  opacity: 1;
}
.s3.animate {
  animation: 1s ease 0s infinite normal forwards pulsate;
}
.shiplayoutvectorsd-icon {
  position: absolute;
  top: 189px;
  left: 666px;
  width: 378px;
  height: 208px;
}
.shiplayoutchambervector-icon {
  position: absolute;
  height: 15.28%;
  width: 13.34%;
  top: 62.2%;
  right: 46.79%;
  bottom: 22.52%;
  left: 39.86%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.shiplayoutellipse-1960 {
  position: absolute;
  height: 2.86%;
  width: 1.52%;
  top: 68.41%;
  right: 52.38%;
  bottom: 28.73%;
  left: 46.1%;
  border-radius: 50%;
  background-color: var(--color-white);
  box-shadow: 0 0 7.51px #fff;
  opacity: 0;
}
.shiplayoutellipse-1960.animate {
  animation: 1s ease 0s infinite normal forwards custom-animation;
}
.shiplayoutvector-746-icon {
  position: absolute;
  height: 34.97%;
  width: 11.93%;
  top: 34.97%;
  right: 41.39%;
  bottom: 30.07%;
  left: 46.68%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  object-fit: contain;
}
.shiplayoutellipse-2 {
  position: absolute;
  top: 315.8px;
  left: 1009.4px;
  border-radius: 50%;
  background-color: var(--color-white);
  box-shadow: 0 0 5px #fff;
  width: 10px;
  height: 10px;
}
.shiplayout-icon,
.shiplayoutstylerectangle-1255-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 506px;
  height: 278px;
}
.shiplayout-icon {
  height: 27.27%;
  width: 81.76%;
  top: 55.72%;
  right: 9.15%;
  bottom: 17.01%;
  left: 9.09%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.shiplayoutsdablink-icon,
.shiplayoutvectorcont-icon,
.veshiplayoutctor-icon {
  position: absolute;
  height: 33.6%;
  width: 79.88%;
  top: 25.68%;
  right: 11.38%;
  bottom: 40.72%;
  left: 8.74%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.shiplayoutvectorcont-icon,
.veshiplayoutctor-icon {
  height: 44.32%;
  width: 78.4%;
  top: 20.14%;
  right: 14.09%;
  bottom: 35.54%;
  left: 7.51%;
}
.shiplayoutvectorcont-icon {
  height: 24.64%;
  width: 17.35%;
  top: 29.5%;
  right: 51.21%;
  bottom: 45.86%;
  left: 31.44%;
  opacity: 1;
}
.shiplayoutvectorcont-icon.animate {
  animation: 1s ease 0s infinite normal forwards pulsate;
}
.shiplayoutstylegroup-48096142 {
  position: absolute;
  top: 43px;
  left: 1168px;
  width: 506px;
  height: 278px;
}
.shiplayoutstylegroup-48096136-icon {
  position: absolute;
  height: 4.38%;
  width: 17.79%;
  top: 9.26%;
  right: 79.61%;
  bottom: 86.36%;
  left: 2.6%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.get-the-submarine {
  position: absolute;
  top: 3.92%;
  left: calc(50% - 356.5px);
  font-size: 99.1px;
  text-transform: uppercase;
  text-shadow: 0 0 24.9px rgba(255, 255, 255, 0.5);
}
.shiplayoutframe,
.vector-icon {
  position: absolute;
  overflow: hidden;
}
.vector-icon {
  height: 16.22%;
  width: 19.58%;
  top: 23.86%;
  right: 41.42%;
  bottom: 59.92%;
  left: 38.99%;
  max-width: 100%;
  max-height: 100%;
}
.shiplayoutframe {
  top: calc(50% - 458.5px);
  left: calc(50% - 866px);
  border-radius: var(--br-6xl);
  width: 1731px;
  height: 918px;
}
.shiplayoutstylecard2 {
  position: absolute;
  top: calc(50% - 483px);
  left: calc(50% - 891px);
  border-radius: 24.59px;
  background-color: rgba(2, 2, 2, 0.85);
  box-shadow: -6px 14px 25px rgba(0, 0, 0, 0.5);
  border: 2px solid var(--color-white);
  box-sizing: border-box;
  width: 1782px;
  height: 967px;
  overflow: hidden;
}
.shiplayout {
  width: 100%;
  position: relative;
  height: 1080px;
  overflow: hidden;
  text-align: left;
  font-size: var(--font-size-14xl-5);
  color: var(--color-white);
  font-family: var(--font-bebas-neue);
}
